// Fill out your copyright notice in the Description page of Project Settings.


#include "Classes/CharacterBase.h"
#include "Kismet/KismetMathLibrary.h"
#include "Net/UnrealNetwork.h"
#include "Components/CapsuleComponent.h"
#include "Components/GASPMC.h"
#include "KismetAnimationLibrary.h"
#include "Kismet/GameplayStatics.h"
#include "Classes/BlockBase.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "ChooserFunctionLibrary.h"
#include "Interfaces/InteractionInterface.h"
#include "PoseSearch/PoseSearchLibrary.h"
#include "PlayMontageCallbackProxy.h"
#include "MotionWarpingComponent.h"
#include "AnimationWarpingLibrary.h"
#include "Classes/WeaponBase.h"
#include "Components/CombatComponent.h"
#include "Animation/AnimInstanceBase.h"

//FName ACharacterBase::MotionWarpingComponentName = TEXT("MotionWarping");

// Sets default values
ACharacterBase::ACharacterBase(const FObjectInitializer& ObjectInitializer) :Super(ObjectInitializer.SetDefaultSubobjectClass<UGASPMC>(ACharacter::CharacterMovementComponentName))
{
 	// Set this character to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = false;

	ChrInputState.WantsToStrafe = true;
	//MotionWarpingComponent = CreateDefaultSubobject<UMotionWarpingComponent>(TEXT("MotionWarping"));

	CombatComponent = CreateDefaultSubobject<UCombatComponent>(TEXT("CombatComponent"));
	CombatComponent->SetIsReplicated(true);

	TempTraversalParams.NewMovementMode = GetCharacterMovement()->MovementMode;
}

// Called when the game starts or when spawned
void ACharacterBase::BeginPlay()
{
	Super::BeginPlay();
}

void ACharacterBase::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME_CONDITION(ACharacterBase, ChrInputState, COND_SkipOwner);
	DOREPLIFETIME_CONDITION(ACharacterBase, TraversalResult, COND_SkipOwner);

	DOREPLIFETIME_CONDITION(ACharacterBase, OverlappedWeapon, COND_OwnerOnly);
}

void ACharacterBase::PostInitializeComponents()
{
	Super::PostInitializeComponents();
	if (CombatComponent)
	{
		CombatComponent->Character = this;
		CombatComponent->AnimInstanceBase = Cast<UAnimInstanceBase>(GetMesh()->GetAnimInstance());
	}

}

void ACharacterBase::Move(FVector2D Value)
{
	IA_MoveValue = Value;
	AddMovementInput(UKismetMathLibrary::GetRightVector(FRotator(0.f,GetControlRotation().Yaw, 0.f)), GetScaleValue(Value).X, false);
	AddMovementInput(UKismetMathLibrary::GetForwardVector(FRotator(0.f, GetControlRotation().Yaw, 0.f)), GetScaleValue(Value).Y, false);
}

void ACharacterBase::MouseInput(FVector2D Value)
{
	AddControllerYawInput(Value.X);
	AddControllerPitchInput(Value.Y);
}

void ACharacterBase::StickInput(FVector2D Value)
{
	AddControllerYawInput(Value.X * UGameplayStatics::GetWorldDeltaSeconds(this));
	AddControllerPitchInput(Value.Y * UGameplayStatics::GetWorldDeltaSeconds(this));
}

void ACharacterBase::WalkInput(bool Newbool)
{
	if (ChrInputState.WantsToSprint) return;
	ChrInputState.WantsToWalk = Newbool;
	Server_SetChrInputState(ChrInputState);
}

void ACharacterBase::SprintInput(bool Newbool)
{
	ChrInputState.WantsToSprint = Newbool;
	ChrInputState.WantsToWalk = false;
	Server_SetChrInputState(ChrInputState);
}

void ACharacterBase::CrouchInput()
{
	if (GetCharacterMovement()->IsFalling()) return;
	bIsCrouched ? UnCrouch() : Crouch();
}

void ACharacterBase::StrafeInput()
{
	ChrInputState.WantsToStrafe = !ChrInputState.WantsToStrafe;
	Server_SetChrInputState(ChrInputState);
}

void ACharacterBase::AimInput(bool Newbool)
{
	ChrInputState.WantsToAim = Newbool;
	Server_SetChrInputState(ChrInputState);
}

void ACharacterBase::EquipWeapon()
{
	if(!CombatComponent) return;
	CombatComponent->EquipButtonPressed();
}

void ACharacterBase::DropWeapon(AWeaponBase* Weapon)
{
	if (!CombatComponent) return;
	CombatComponent->DropButtonPressed(Weapon);
}

void ACharacterBase::SwapWeapon()
{
	if (!CombatComponent) return;
	CombatComponent->SwapButtonPressed();
}


void ACharacterBase::FireWeapon(bool Newbool)
{
	if (!CombatComponent || !CombatComponent->EquipedWeapon) return;
	CombatComponent->FireWeapon(Newbool);
}

void ACharacterBase::PerformTrace()
{
	if (GetController())
	{
		FHitResult OutHit;
		FCollisionQueryParams CollisionParams;
		CollisionParams.AddIgnoredActor(this);

		FVector EyesLoc;
		FRotator EyesRot;
		GetController()->GetPlayerViewPoint(EyesLoc, EyesRot);

		const FVector End = (EyesRot.Vector() * 2000.f) + EyesLoc;

		GetWorld()->LineTraceSingleByChannel(OutHit, EyesLoc, End, ECC_GameTraceChannel2, CollisionParams);
		Server_FireWeapon(OutHit);
	}
}


void ACharacterBase::Server_FireWeapon_Implementation(FHitResult InHit)
{
	FCollisionQueryParams CollisionParams;
	CollisionParams.AddIgnoredActor(this);

	GetWorld()->LineTraceSingleByChannel(InHit, InHit.TraceStart, InHit.TraceEnd, ECC_GameTraceChannel2, CollisionParams);
	if (!InHit.bBlockingHit) return;

	ACharacterBase* HitCharacter = Cast<ACharacterBase>(InHit.GetActor());
	if (!HitCharacter) return;

	UGameplayStatics::ApplyPointDamage(HitCharacter, CombatComponent->EquipedWeapon->BaseDamage, InHit.TraceStart - InHit.TraceEnd, InHit, GetController(), 
		                                                                                                                 this, UDamageType::StaticClass());
}

void ACharacterBase::Multicast_HitReact_Implementation(AActor* Damager)
{
	if (GetMesh()->GetAnimInstance()->IsAnyMontagePlaying()) return;

	FVector EyesLoc;
	FRotator EyesRot;
	GetActorEyesViewPoint(EyesLoc, EyesRot);

	FRotator HitRotation = UKismetMathLibrary::NormalizedDeltaRotator(EyesRot, UKismetMathLibrary::FindLookAtRotation(EyesLoc, Damager->GetActorLocation()));

	UAnimMontage* SelectedMontage = CombatComponent->BackHit;
	if (UKismetMathLibrary::InRange_FloatFloat(HitRotation.Yaw, -45.f, 45.f, false, false)) SelectedMontage = CombatComponent->FrontHit;
	if (UKismetMathLibrary::InRange_FloatFloat(HitRotation.Yaw, 45.f, 135.f, true, true)) SelectedMontage = CombatComponent->RightHit;
	if (UKismetMathLibrary::InRange_FloatFloat(HitRotation.Yaw, -135.f, -45.f, true, true)) SelectedMontage = CombatComponent->LeftHit;


	PlayAnimMontage(SelectedMontage, 2.f);
}


void ACharacterBase::Landed(const FHitResult& Hit)
{
	FinishJump(GetCharacterMovement()->Velocity);
}

void ACharacterBase::FinishJump(FVector InVel)
{
	LandVelocity = InVel;
	PlayAudio(UKismetMathLibrary::MapRangeClamped(LandVelocity.Z, -500.f, -900.f, 0.5f, 1.5f), false);
	JustLanded = true;

	FLatentActionInfo LatentInfo;
	LatentInfo.CallbackTarget = this;
	LatentInfo.ExecutionFunction = "OnDelayCompleted";
	LatentInfo.Linkage = 0;
	LatentInfo.UUID = __LINE__;

	CustomDelay(this, 0.3f, LatentInfo);
}

void ACharacterBase::Jump()
{
	if (DoingTraversalAction) return;
	if (!GetCharacterMovement()->IsMovingOnGround()) return;

	bool ActionFailed = TryTraversalAction();
	if (ActionFailed)
	{
		Super::Jump();
		PlayAudio(UKismetMathLibrary::MapRangeClamped(GetCharacterMovement()->Velocity.Size2D(), 0.f, 500.f, 0.5f, 1.f), true);
	}
}

void ACharacterBase::CustomDelay(UObject* WorldContextObject, float Duration, FLatentActionInfo LatentInfo)
{
	if (UWorld* World = GEngine->GetWorldFromContextObjectChecked(WorldContextObject))
	{
		FLatentActionManager& LatentManager = World->GetLatentActionManager();
		if (LatentManager.FindExistingAction<FFireDelayLatentAction>(LatentInfo.CallbackTarget, LatentInfo.UUID) == nullptr)
		{
			LatentManager.AddNewAction(LatentInfo.CallbackTarget, LatentInfo.UUID, new FFireDelayLatentAction(Duration, LatentInfo));
		}
	}
}

void ACharacterBase::OnDelayCompleted()
{
	JustLanded = false;
}




FVector2D ACharacterBase::GetScaleValue(FVector2D Value)
{
	switch (StickBehavior)
	{
	case EAnalogStickBehavior::FixedSpeedSingleGait: return UKismetMathLibrary::Normal2D(Value);
		break;
	case EAnalogStickBehavior::FixedSpeedWalkRun: return UKismetMathLibrary::Normal2D(Value);
		break;
	case EAnalogStickBehavior::VariableSpeedSingleGait: return Value;
		break;
	case EAnalogStickBehavior::VariableSpeedWalkRun: return Value;
		break;
	default: return Value;
		break;
	}
}

void ACharacterBase::UpdateMovementSimulated(FVector OldVelocity)
{
	IsMovingOnGround = GetCharacterMovement()->IsMovingOnGround();
	if (IsMovingOnGround != WasMovingOnGroundSimulated)
	{
		(IsMovingOnGround) ? FinishJump(OldVelocity) : PlayAudio(UKismetMathLibrary::VSizeXY(OldVelocity), true);
	}
	WasMovingOnGroundSimulated = IsMovingOnGround;
}

bool ACharacterBase::CanSprint()
{
	return ChrInputState.WantsToSprint && GetCharacterMovement()->bOrientRotationToMovement ? true : ChrInputState.WantsToSprint &&
		      UKismetMathLibrary::Abs(UKismetMathLibrary::NormalizedDeltaRotator(GetActorRotation(), UKismetMathLibrary::MakeRotFromX(IsLocallyControlled() ? 
			  GetPendingMovementInputVector() : GetCharacterMovement()->GetCurrentAcceleration())).Yaw) < 50.f;
}

EGaits ACharacterBase::UpdateGait()
{
	bool FullMovementInput = false;
	switch (StickBehavior)
	{
	case EAnalogStickBehavior::FixedSpeedSingleGait: FullMovementInput = true;
		break;
	case EAnalogStickBehavior::FixedSpeedWalkRun: FullMovementInput = UKismetMathLibrary::VSize2D(IA_MoveValue) >= AnalogWalkRunThreshold;
		break;
	case EAnalogStickBehavior::VariableSpeedSingleGait: FullMovementInput = true;
		break;
	case EAnalogStickBehavior::VariableSpeedWalkRun: FullMovementInput = UKismetMathLibrary::VSize2D(IA_MoveValue) >= AnalogWalkRunThreshold;
		break;
	default: FullMovementInput = false;
		break;
	}
	return (CanSprint()) ? FullMovementInput ? EGaits::Sprinting : EGaits::Jogging : 
   (ChrInputState.WantsToWalk) ? EGaits::Walking : FullMovementInput ? EGaits::Jogging : EGaits::Walking;
}

float ACharacterBase::CalculateBrakingDecelerationWalking()
{
	return UKismetMathLibrary::NotEqual_VectorVector(GetPendingMovementInputVector(), FVector::ZeroVector, 0.f) ? 500.f : 2000.f;
}

float ACharacterBase::CalculateGroundFriction()
{
	if (Gait != EGaits::Sprinting) return 5.f;
	return UKismetMathLibrary::MapRangeClamped(UKismetMathLibrary::VSizeXY(GetCharacterMovement()->Velocity), 0.f, 500.f, 5.f, 3.f);
}

float ACharacterBase::CalculateMaxSpeedCrouched()
{
	if (!StrafeSpeedCurve) return 0.f;

	float StrafeSpeedMap = GetCharacterMovement()->bUseControllerDesiredRotation ?
		                   StrafeSpeedCurve->GetFloatValue(UKismetMathLibrary::Abs(UKismetAnimationLibrary::
						   CalculateDirection(GetCharacterMovement()->Velocity, GetActorRotation()))) : 0.f;

	return StrafeSpeedMap < 1.f ? UKismetMathLibrary::MapRangeClamped(StrafeSpeedMap, 0.f, 1.f, CrouchSpeed.X, CrouchSpeed.Y) : 
		                          UKismetMathLibrary::MapRangeClamped(StrafeSpeedMap, 1.f, 2.f, CrouchSpeed.Y, CrouchSpeed.Z);
}

float ACharacterBase::CalculateBrakingFriction()
{
	return UKismetMathLibrary::NotEqual_VectorVector(GetCharacterMovement()->GetCurrentAcceleration(), FVector::ZeroVector, 0.f) ? 3.f : 0.f;
}

float ACharacterBase::CalculateMaxAcceleration()
{
	switch (Gait)
	{
	case EGaits::Walking:return 800.f;
		break;
	case EGaits::Jogging:return 800.f;
		break;
	case EGaits::Sprinting: return UKismetMathLibrary::MapRangeClamped(GetCharacterMovement()->Velocity.Size2D(), 300.f, 700.f, 800.f, 300.f);
		break;
	default: return 0.0f;
		break;
	}
}

float ACharacterBase::CalculateMaxSpeed()
{
	if (!StrafeSpeedCurve) return 0.f;

	float StrafeSpeedMap = GetCharacterMovement()->bUseControllerDesiredRotation ?
		StrafeSpeedCurve->GetFloatValue(UKismetMathLibrary::Abs(UKismetAnimationLibrary::CalculateDirection(GetCharacterMovement()->Velocity, GetActorRotation()))) : 0.f;

	FVector SelectedSpeed = FVector::ZeroVector;
	switch (Gait)
	{
	case EGaits::Walking: SelectedSpeed = WalkSpeed;
		break;
	case EGaits::Jogging: SelectedSpeed = RunSpeed;
		break;
	case EGaits::Sprinting: SelectedSpeed = SprintSpeed;
		break;
	default:
		break;
	}

	if (StrafeSpeedMap < 1.f) return UKismetMathLibrary::MapRangeClamped(StrafeSpeedMap, 0.f, 1.f, SelectedSpeed.X, SelectedSpeed.Y);
	return UKismetMathLibrary::MapRangeClamped(StrafeSpeedMap, 1.f, 2.f, SelectedSpeed.Y, SelectedSpeed.Z);
}


// Traversal

bool ACharacterBase::TryTraversalAction()
{
	bool TestBool = false;

	CacheValues();
	TestBool = StoreHitComponent();
	if (TestBool) return true;

	if (!TempTraversalParams.HasFrontLedge) return true;

	TestBool = HasRoom();
	if (!TestBool) return true;

	SetObstacleHeight();
	TestBool = TopSweep();
	SetObstacleDepth(TestBool);
	DownwardTrace();

	GetAllMatchingMontages();
	if (ValidMontages.IsEmpty()) return true;

	SetInteractionTransform();
	SelectTraversalMontage();
	if (!TempTraversalParams.ChosenMontage) return true;


	TraversalResult = TempTraversalParams;
	PerformTraversalAction();
	Server_Traversal(TraversalResult);

	return false;
}

FTraversalCheckInputs ACharacterBase::GetTraversalInputs()
{
	FTraversalCheckInputs NewInputs;
	NewInputs.TraceForwardDirection = GetActorForwardVector();
	NewInputs.TraceRadius = 30.f;
	switch (GetCharacterMovement()->MovementMode)
	{
	case MOVE_None:
		NewInputs.TraceForwardDistence = UKismetMathLibrary::MapRangeClamped(UKismetMathLibrary::Quat_UnrotateVector(GetActorRotation().Quaternion(), GetCharacterMovement()->Velocity).X, 0.f, 500.f, 75.f, 350.f);
		NewInputs.TraceHalfHeight = 60.f;
		break;
	case MOVE_Walking:
		NewInputs.TraceForwardDistence = UKismetMathLibrary::MapRangeClamped(UKismetMathLibrary::Quat_UnrotateVector(GetActorRotation().Quaternion(), GetCharacterMovement()->Velocity).X, 0.f, 500.f, 75.f, 350.f);
		NewInputs.TraceHalfHeight = 60.f;
		break;
	case MOVE_NavWalking:
		NewInputs.TraceForwardDistence = UKismetMathLibrary::MapRangeClamped(UKismetMathLibrary::Quat_UnrotateVector(GetActorRotation().Quaternion(), GetCharacterMovement()->Velocity).X, 0.f, 500.f, 75.f, 350.f);
		NewInputs.TraceHalfHeight = 60.f;
		break;
	case MOVE_Falling:
		NewInputs.TraceForwardDistence = 75.f;
		NewInputs.TraceHalfHeight = 86.f;
		break;
	case MOVE_Swimming:
		NewInputs.TraceForwardDistence = UKismetMathLibrary::MapRangeClamped(UKismetMathLibrary::Quat_UnrotateVector(GetActorRotation().Quaternion(), GetCharacterMovement()->Velocity).X, 0.f, 500.f, 75.f, 350.f);
		NewInputs.TraceHalfHeight = 60.f;
		break;
	case MOVE_Flying:
		NewInputs.TraceForwardDistence = 75.f;
		NewInputs.TraceHalfHeight = 86.f;
		break;
	case MOVE_Custom:
		NewInputs.TraceForwardDistence = UKismetMathLibrary::MapRangeClamped(UKismetMathLibrary::Quat_UnrotateVector(GetActorRotation().Quaternion(), GetCharacterMovement()->Velocity).X, 0.f, 500.f, 75.f, 350.f);
		NewInputs.TraceHalfHeight = 60.f;
		break;
	case MOVE_MAX:
		break;
	default:
		break;
	}
	return NewInputs;
}

void ACharacterBase::CacheValues()
{
	ActorLocation = GetActorLocation();

	CapsuleRadius = GetCapsuleComponent()->GetScaledCapsuleRadius();
	CapsuleHalfHeight = GetCapsuleComponent()->GetScaledCapsuleHalfHeight();
}

FHitResult ACharacterBase::TraversalTrace(FVector Start, FVector End, float Radius, float HalfHeight) const
{
	FHitResult HitResult;
	TArray<AActor*> IgnoredActors;
	ETraceTypeQuery TraversalQuery = UEngineTypes::ConvertToTraceType(ECC_Visibility);
	UKismetSystemLibrary::CapsuleTraceSingle(this, Start, End, Radius, HalfHeight, TraversalQuery, false, IgnoredActors, EDrawDebugTrace::None, HitResult, true);

	return HitResult;
}

bool ACharacterBase::StoreHitComponent()
{
	FTraversalCheckInputs Inputs = GetTraversalInputs();
	FVector Start = ActorLocation + Inputs.TraceOriginOffset;
	FVector End = Start + (Inputs.TraceForwardDirection * Inputs.TraceForwardDistence) + Inputs.TraceEndOffset;
	FHitResult NewHit = TraversalTrace(Start, End, Inputs.TraceRadius, Inputs.TraceHalfHeight);
	if (!NewHit.bBlockingHit) return true;

	ABlockBase* Block = Cast<ABlockBase>(NewHit.GetActor());
	if (!Block) return true;
	TempTraversalParams.HitComponent = NewHit.GetComponent();

	Block->GetLedgeTransform(NewHit.ImpactPoint, ActorLocation, this);
	return false;
}

bool ACharacterBase::HasRoom()
{
	HasRoomCheckFrontLedgeLocation = TempTraversalParams.FrontLedgeLocation + (TempTraversalParams.FrontLedgeNormal * CapsuleRadius + 2.f) + FVector(0.f, 0.f, CapsuleHalfHeight + 2.f);
	FHitResult NewResult = TraversalTrace(ActorLocation, HasRoomCheckFrontLedgeLocation, CapsuleRadius * 0.6f, CapsuleHalfHeight);
	bool Newbool = UKismetMathLibrary::BooleanNOR(NewResult.bBlockingHit, NewResult.IsValidBlockingHit());
	if (!Newbool) TempTraversalParams.HasFrontLedge = false;

	return Newbool;
}

void ACharacterBase::SetObstacleHeight()
{
	TempTraversalParams.ObstacleHeight = UKismetMathLibrary::Abs(((ActorLocation - FVector(0.f, 0.f, CapsuleHalfHeight)) - TempTraversalParams.FrontLedgeLocation).Z);
}

bool ACharacterBase::TopSweep()
{
	FVector HeightZ = FVector(0.f, 0.f, CapsuleHalfHeight + 2.f);
	HasRoomCheckBackLedgeLocation = TempTraversalParams.BackLedgeLocation + TempTraversalParams.BackLedgeNormal * (CapsuleRadius + 2.f) + HeightZ;
	TopSweepResult = TraversalTrace(HasRoomCheckFrontLedgeLocation, HasRoomCheckBackLedgeLocation, CapsuleRadius, CapsuleHalfHeight);
	return !TopSweepResult.bBlockingHit;
}

void ACharacterBase::SetObstacleDepth(bool HasRoom)
{
	if (!HasRoom)
	{
		TempTraversalParams.ObstacleDepth = (TopSweepResult.ImpactPoint - TempTraversalParams.FrontLedgeLocation).Size2D();
		TempTraversalParams.HasBackLedge = false;
		return;
	}

	TempTraversalParams.ObstacleDepth = (TempTraversalParams.FrontLedgeLocation - TempTraversalParams.BackLedgeLocation).Size2D();
}

void ACharacterBase::DownwardTrace()
{
	FVector HeightZ = FVector(0.f, 0.f, (TempTraversalParams.ObstacleHeight - CapsuleHalfHeight) + 50.f);
	FVector End = (TempTraversalParams.BackLedgeLocation + TempTraversalParams.BackLedgeNormal * (CapsuleRadius + 2.f)) - HeightZ;
	FHitResult DownHit = TraversalTrace(HasRoomCheckBackLedgeLocation, End, CapsuleRadius, CapsuleHalfHeight);

	if (DownHit.bBlockingHit)
	{
		TempTraversalParams.HasBackFloor = true;
		TempTraversalParams.BackFloorLocation = DownHit.ImpactPoint;
		TempTraversalParams.BackLedgeHeight = UKismetMathLibrary::Abs((DownHit.ImpactPoint - TempTraversalParams.BackLedgeLocation).Z);
	}
	else TempTraversalParams.HasBackFloor = false;
}

void ACharacterBase::GetAllMatchingMontages()
{
	if (!TraversalTable) return;
	ValidMontages = UChooserFunctionLibrary::EvaluateChooserMulti(this, TraversalTable, UAnimMontage::StaticClass());
}

void ACharacterBase::SetInteractionTransform()
{
	if (IInteractionInterface* AnimInterface = Cast<IInteractionInterface>(GetMesh()->GetAnimInstance()))
		AnimInterface->SetInteractionTransform(FTransform(UKismetMathLibrary::MakeRotFromZ(TempTraversalParams.FrontLedgeNormal),
			                                                    TempTraversalParams.FrontLedgeLocation, FVector(1.f, 1.f, 1.f)));
}

void ACharacterBase::SelectTraversalMontage()
{
	FPoseSearchBlueprintResult Result;
	UPoseSearchLibrary::MotionMatch(GetMesh()->GetAnimInstance(), ValidMontages, "PoseHistory", FPoseSearchContinuingProperties(), FPoseSearchFutureProperties(), Result);

	TempTraversalParams.ChosenMontage = Cast<UAnimMontage>(Result.SelectedAnimation);
	if (!TempTraversalParams.ChosenMontage) return;
	TempTraversalParams.StartTime = Result.SelectedTime;
	TempTraversalParams.PlayRate = Result.WantedPlayRate;
}

void ACharacterBase::UpdateWarpTargets()
{
	float DistanceFromFrontLedgeToBackLedge = 0.f;
	float DistanceFromFrontLedgeToBackFloor = 0.f;

	MotionWarpingComponent->AddOrUpdateWarpTargetFromLocationAndRotation("FrontLedge", TraversalResult.FrontLedgeLocation,
		               UKismetMathLibrary::MakeRotFromX(UKismetMathLibrary::NegateVector(TraversalResult.FrontLedgeNormal)));

	if (TraversalResult.ActionType == ETraversalActionType::Hurdle || TraversalResult.ActionType == ETraversalActionType::Vault)
	{
		TArray<FMotionWarpingWindowData> OutData;
		UMotionWarpingUtilities::GetMotionWarpingWindowsForWarpTargetFromAnimation(TraversalResult.ChosenMontage, "BackLedge", OutData);
		if (OutData.IsEmpty()) MotionWarpingComponent->RemoveWarpTarget("BackLedge");
		else
		{
			UAnimationWarpingLibrary::GetCurveValueFromAnimation(TraversalResult.ChosenMontage, "Distance_From_Ledge", OutData[0].EndTime,
				                                                                                       DistanceFromFrontLedgeToBackLedge);
			MotionWarpingComponent->AddOrUpdateWarpTargetFromLocationAndRotation("BackLedge", TraversalResult.BackLedgeLocation, FRotator::ZeroRotator);
		}
	}
	else MotionWarpingComponent->RemoveWarpTarget("BackLedge");

	if (TempTraversalParams.ActionType == ETraversalActionType::Hurdle)
	{
		TArray<FMotionWarpingWindowData> OutData2;
		UMotionWarpingUtilities::GetMotionWarpingWindowsForWarpTargetFromAnimation(TraversalResult.ChosenMontage, "BackFloor", OutData2);
		if (OutData2.IsEmpty()) MotionWarpingComponent->RemoveWarpTarget("BackFloor");
		else
		{
			UAnimationWarpingLibrary::GetCurveValueFromAnimation(TraversalResult.ChosenMontage, "Distance_From_Ledge", OutData2[0].EndTime,
				DistanceFromFrontLedgeToBackFloor);

			FVector TempLoc(FVector(TraversalResult.BackLedgeLocation + (TraversalResult.BackLedgeNormal * UKismetMathLibrary::Abs
			                                                (DistanceFromFrontLedgeToBackLedge - DistanceFromFrontLedgeToBackFloor))));
			FVector NewLoc(TempLoc.X, TempLoc.Y, TraversalResult.BackFloorLocation.Z);
			MotionWarpingComponent->AddOrUpdateWarpTargetFromLocationAndRotation("BackFloor", NewLoc, FRotator::ZeroRotator);
		}

	}
	else MotionWarpingComponent->RemoveWarpTarget("BackFloor");
}

void ACharacterBase::PerformTraversalAction()
{
	UpdateWarpTargets();

	UAnimMontage* MontageToPlay = const_cast<UAnimMontage*>(TraversalResult.ChosenMontage);
	if (!MontageToPlay) return;
	if (IInteractionInterface* AnimInterface = Cast<IInteractionInterface>(GetMesh()->GetAnimInstance()))
		AnimInterface->PlayTraversalMontage(MontageToPlay, TraversalResult.PlayRate, TraversalResult.StartTime);

	OnMontageCompleted(true);
}

void ACharacterBase::OnRep_TraversalResult()
{
	PerformTraversalAction();
}

void ACharacterBase::OnTraversal(bool Newbool)
{
	GetCharacterMovement()->bIgnoreClientMovementErrorChecksAndCorrection = Newbool;
	GetCharacterMovement()->bServerAcceptClientAuthoritativePosition = Newbool;
}

void ACharacterBase::SetAnimParam(UAnimMontage* InMontage)
{
	TempTraversalParams.ChosenMontage = InMontage;
}



// Play Montage

void ACharacterBase::PlayMontage(UAnimMontage* Montage, float PlayRate)
{
	if (!Montage) return;
	GetMesh()->GetAnimInstance()->Montage_Play(Montage, PlayRate);
}





void ACharacterBase::OnRep_OverlappedWeapon(AWeaponBase* LastWeapon)
{
	if (OverlappedWeapon) OverlappedWeapon->ShowWidget(true);
	if (LastWeapon) LastWeapon->ShowWidget(false);
}

void ACharacterBase::SetOverlappedWeapon(AWeaponBase* Weapon)
{
	if (OverlappedWeapon) OverlappedWeapon->ShowWidget(false);
	OverlappedWeapon = Weapon;
	if (IsLocallyControlled())
	{
		if (OverlappedWeapon) OverlappedWeapon->ShowWidget(true);
	}
	
}

ECharacterState ACharacterBase::GetCharacterState() const
{
	return CombatComponent ? CombatComponent->CharacterState : ECharacterState();
}

AWeaponBase* ACharacterBase::GetEquipedWeapon() const
{
	return CombatComponent ? CombatComponent->EquipedWeapon : nullptr;
}



// Montage And Delegates

void ACharacterBase::OnMontageCompleted(bool Newbool)
{
	DoingTraversalAction = Newbool;
	OnTraversal(Newbool);
	GetCapsuleComponent()->IgnoreComponentWhenMoving(TraversalResult.HitComponent, Newbool);

	GetCharacterMovement()->SetMovementMode((Newbool) ? EMovementMode::MOVE_Flying : EMovementMode::MOVE_Walking, 0);
}



// Replication

void ACharacterBase::Server_SetChrInputState_Implementation(FInputState NewState)
{
	ChrInputState = NewState;
}

void ACharacterBase::Server_Traversal_Implementation(FTraversalParams InTraversalParams)
{
	TraversalResult = InTraversalParams;
	PerformTraversalAction();
}