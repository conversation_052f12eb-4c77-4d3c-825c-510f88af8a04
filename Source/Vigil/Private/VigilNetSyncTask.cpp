// Copyright (c) <PERSON>


#include "VigilNetSyncTask.h"
#include "AbilitySystemComponent.h"

#include UE_INLINE_GENERATED_CPP_BY_NAME(VigilNetSyncTask)


UVigilNetSyncTask::UVigilNetSyncTask(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
{
	ReplicatedEventToListenFor = EAbilityGenericReplicatedEvent::MAX;
	SyncType = EVigilNetSyncType::OnlyServerWait;
}

void UVigilNetSyncTask::OnSignalCallback()
{
	TRACE_CPUPROFILER_EVENT_SCOPE(VigilNetSyncTask::OnSignalCallback);
	
	if (AbilitySystemComponent.IsValid())
	{
		AbilitySystemComponent->ConsumeGenericReplicatedEvent(ReplicatedEventToListenFor, GetAbilitySpecHandle(), GetActivationPredictionKey());
	}
	SyncFinished();
}

UVigilNetSyncTask* UVigilNetSyncTask::WaitNetSync(class UGameplayAbility* OwningAbility, EVigilNetSyncType InSyncType)
{
	TRACE_CPUPROFILER_EVENT_SCOPE(VigilNetSyncTask::WaitNetSync);
	
	UVigilNetSyncTask* MyObj = NewAbilityTask<UVigilNetSyncTask>(OwningAbility);
	MyObj->SyncType = InSyncType;
	return MyObj;
}

void UVigilNetSyncTask::Activate()
{
	TRACE_CPUPROFILER_EVENT_SCOPE(VigilNetSyncTask::Activate);

	FScopedPredictionWindow ScopedPrediction(AbilitySystemComponent.Get(), IsPredictingClient());

	if (AbilitySystemComponent.IsValid())
	{
		if (IsPredictingClient())
		{
			if (SyncType != EVigilNetSyncType::OnlyServerWait )
			{
				// As long as we are waiting (!= OnlyServerWait), listen for the GenericSignalFromServer event
				ReplicatedEventToListenFor = EAbilityGenericReplicatedEvent::GenericSignalFromServer;
			}
			if (SyncType != EVigilNetSyncType::OnlyClientWait)
			{
				// As long as the server is waiting (!= OnlyClientWait), send the Server and RPC for this signal
				AbilitySystemComponent->ServerSetReplicatedEvent(EAbilityGenericReplicatedEvent::GenericSignalFromClient, GetAbilitySpecHandle(), GetActivationPredictionKey(), AbilitySystemComponent->ScopedPredictionKey);
			}
			
		}
		else if (IsForRemoteClient())
		{
			if (SyncType != EVigilNetSyncType::OnlyClientWait )
			{
				// As long as we are waiting (!= OnlyClientWait), listen for the GenericSignalFromClient event
				ReplicatedEventToListenFor = EAbilityGenericReplicatedEvent::GenericSignalFromClient;
			}
			if (SyncType != EVigilNetSyncType::OnlyServerWait)
			{
				// As long as the client is waiting (!= OnlyServerWait), send the Server and RPC for this signal
				AbilitySystemComponent->ClientSetReplicatedEvent(EAbilityGenericReplicatedEvent::GenericSignalFromServer, GetAbilitySpecHandle(), GetActivationPredictionKey());
			}
		}

		if (ReplicatedEventToListenFor != EAbilityGenericReplicatedEvent::MAX)
		{
			CallOrAddReplicatedDelegate(ReplicatedEventToListenFor, FSimpleMulticastDelegate::FDelegate::CreateUObject(this, &UVigilNetSyncTask::OnSignalCallback));
		}
		else
		{
			// We aren't waiting for a replicated event, so the sync is complete.
			SyncFinished();
		}
	}
}

void UVigilNetSyncTask::SyncFinished()
{
	TRACE_CPUPROFILER_EVENT_SCOPE(VigilNetSyncTask::SyncFinished);
	
	if (IsValid(this))
	{
		if (ShouldBroadcastAbilityTaskDelegates())
		{
			OnSync.ExecuteIfBound(this);
		}
		EndTask();
	}
}

