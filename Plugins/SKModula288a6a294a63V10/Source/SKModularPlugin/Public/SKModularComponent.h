// Copyright 2019-2022 <PERSON>. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "SKModularFunctionLibrary.h"
#include "SKModularComponent.generated.h"

//Used to store informations about current state of SKModularComponent ||| Can manage multiple skeletals per component if it's not SKModularGore
USTRUCT()
struct SKMODULARPLUGIN_API FSkeletalMeshComponentManagerList
{
	GENERATED_USTRUCT_BODY()
	// Skeletal Mesh Target name to store.
	UPROPERTY()
		FName SkeletalComponentName;
	// Simulated cloths references attached to this skeletal.
	UPROPERTY()
		TArray<USkeletalMeshComponent*> AttachedCloths;
	// Current Skeletals data for this component
	UPROPERTY()
		FCustomSkeletalMeshMergeParams CurrentParams;

	FSkeletalMeshComponentManagerList()
	{
		SkeletalComponentName = FName();
		AttachedCloths = TArray<USkeletalMeshComponent*>();
		CurrentParams = FCustomSkeletalMeshMergeParams();
	}
};

UCLASS( ClassGroup=(Custom), meta=(BlueprintSpawnableComponent) )
class SKMODULARPLUGIN_API USKModularComponent : public UActorComponent
{
	GENERATED_UCLASS_BODY()

public:	
	// Sets default values for this component's properties
	USKModularComponent();

	//Saved informations about skeletal meshes managed by this component
	UPROPERTY()
		TArray<FSkeletalMeshComponentManagerList> ComponentsInfos;

protected:

public:
	//Used to find the inex of a Skeletal Mesh Component managed by this component referenced inside "ComponentsInfos"
	UFUNCTION()
		virtual int32 GetTargetIndexList(USkeletalMeshComponent* Target);
	//Used to set current parameters about a Skeletal Mesh Component inside "ComponentsInfos"
	UFUNCTION()
		virtual int32 SetCurrentParams(const FCustomSkeletalMeshMergeParams& MergeParams, USkeletalMeshComponent* Target, FCustomSkeletalMeshMergeParams& OutParams);
	//Main function used to merge multiple meshes, reserved for C++ purposes
	UFUNCTION()
		virtual bool MergeSkeletalMeshesComp(const FCustomSkeletalMeshMergeParams& MergeParams, bool FreezeCloths, USkeletalMeshComponent* Target);	

	/**
	* Target - Skeletal Mesh component you want to get params
	* @Found - Will return true if found the skeletal mesh component params
	* @return SKModular parameters for this component
	*/
	UFUNCTION(BlueprintPure, Category = "SKModular", meta = (UnsafeDuringActorConstruction = "true"))
		FCustomSkeletalMeshMergeParams GetCurrentMeshParams(USkeletalMeshComponent* Target, bool& Found);

	/**
	* Merge the given meshes into a single mesh.
	* MergeParams - Parameters to use (Skeletal Meshes, Physics Asset Override, Skeleton Override...)
	* FreezeCloths - Should merge simulated cloths too? (this will disable cloth simulation)
	* Target - Skeletal Mesh component you want to apply the merged skeletal mesh
	* @return false if the merge failed.
	*/
	UFUNCTION(BlueprintCallable, Category = "SKModular", meta = (UnsafeDuringActorConstruction = "true"))
		bool MergeSkeletalMeshesAndApplyToComponent(const FCustomSkeletalMeshMergeParams& MergeParams, bool FreezeCloths, USkeletalMeshComponent* SkeletalMeshCompTarget);
	/**
	* Add skeletal mesh and merge the given meshes into a single mesh.
	* NewSkeletal - Skeletal mesh to add
	* FreezeCloths - Should merge simulated cloths too? (this will disable cloth simulation)
	* Target - Skeletal Mesh component you want to apply the merged skeletal mesh
	* @return false if the merge failed.
	*/
	UFUNCTION(BlueprintCallable, Category = "SKModular", meta = (UnsafeDuringActorConstruction = "true"))
		virtual bool AddSkeletalMesh(USkeletalMesh* NewSkeletal, bool FreezeCloths, USkeletalMeshComponent* SkeletalMeshCompTarget);
	/**
	* Add multiple skeletal meshes and merge the given meshes into a single mesh.
	* NewSkeletals - Skeletal meshes to add (Array)
	* FreezeCloths - Should merge simulated cloths too? (this will disable cloth simulation)
	* Target - Skeletal Mesh component you want to apply the merged skeletal mesh
	* @return false if the merge failed.
	*/
	UFUNCTION(BlueprintCallable, Category = "SKModular", meta = (UnsafeDuringActorConstruction = "true"))
		virtual bool AddMultipleSkeletalMeshes(TArray<USkeletalMesh*> NewSkeletals, bool FreezeCloths, USkeletalMeshComponent* SkeletalMeshCompTarget);
	/**
	* Remove skeletal mesh and merge the given meshes into a single mesh.
	* Skeletal - Skeletal mesh to remove
	* FreezeCloths - Should merge simulated cloths too? (this will disable cloth simulation)
	* Target - Skeletal Mesh component you want to apply the merged skeletal mesh
	* @return false if the merge failed.
	*/
	UFUNCTION(BlueprintCallable, Category = "SKModular", meta = (UnsafeDuringActorConstruction = "true"))
		virtual bool RemoveSkeletalMesh(USkeletalMesh* Skeletal, bool FreezeCloths, USkeletalMeshComponent* SkeletalMeshCompTarget);
	/**
	* Remove multiple skeletal meshes and merge the given meshes into a single mesh.
	* Skeletals - Skeletal meshes to Remove (Array)
	* FreezeCloths - Should merge simulated cloths too? (this will disable cloth simulation)
	* Target - Skeletal Mesh component you want to apply the merged skeletal mesh
	* @return false if the merge failed.
	*/
	UFUNCTION(BlueprintCallable, Category = "SKModular", meta = (UnsafeDuringActorConstruction = "true"))
		virtual bool RemoveMultipleSkeletalMeshes(TArray<USkeletalMesh*> Skeletals, bool FreezeCloths, USkeletalMeshComponent* SkeletalMeshCompTarget);
		
};
