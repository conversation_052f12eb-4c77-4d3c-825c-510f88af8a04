{"FileVersion": 3, "Version": 1, "VersionName": "1.3.1", "FriendlyName": "SKGShooterFramework", "Description": "SKG Multiplayer Shooter Framework", "Category": "SKG Plugins", "CreatedBy": "Sneaky Kitty Game Dev", "CreatedByURL": "https://www.youtube.com/channel/UCYcDJPGvIUFxBTmXOZWFswQ", "DocsURL": "https://github.com/SneakyKittyGameDev/SKGSFExample/wiki", "MarketplaceURL": "com.epicgames.launcher://ue/marketplace/product/4986d4f5b73148deb154e1cd31dd9623", "SupportURL": "https://discord.gg/W5g6pZXfjh", "EngineVersion": "5.5.0", "CanContainContent": false, "Installed": true, "Modules": [{"Name": "SKGShooterFramework", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux", "<PERSON>"]}, {"Name": "SKGShooterFrameworkEditor", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux", "<PERSON>"]}, {"Name": "SKGShooterFrameworkCore", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux", "<PERSON>"]}, {"Name": "SKGStats", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux", "<PERSON>"]}, {"Name": "SKGProjectile", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux", "<PERSON>"]}, {"Name": "SKGPhysicalMaterialModule", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux", "<PERSON>"]}, {"Name": "SKGAnimGraphNodes", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux", "<PERSON>"]}, {"Name": "SKGAttachment", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux", "<PERSON>"]}, {"Name": "SKGLightLaser", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux", "<PERSON>"]}, {"Name": "SKGProceduralAnim", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux", "<PERSON>"]}, {"Name": "SKGMuzzle", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux", "<PERSON>"]}, {"Name": "SKGOptic", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux", "<PERSON>"]}, {"Name": "SKGRangeFinder", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux", "<PERSON>"]}, {"Name": "SKGStats", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux", "<PERSON>"]}, {"Name": "SKGStock", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux", "<PERSON>"]}, {"Name": "SKGOptional", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux", "<PERSON>"]}, {"Name": "SKGShooterFrameworkActors", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "Niagara", "Enabled": true}]}