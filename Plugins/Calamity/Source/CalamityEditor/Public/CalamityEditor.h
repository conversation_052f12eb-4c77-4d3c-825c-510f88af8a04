// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "AssetTypeCategories.h"
#include "IAssetTools.h"
#include "Modules/ModuleInterface.h"
#include "Modules/ModuleManager.h"

#include "Customizations/WeaponItemDataAssetCustomization.h"

struct FGameplayEffectContext;
class IAssetTypeActions;

class FCalamityEditorModule : public IModuleInterface
{
public:
    /** IModuleInterface implementation */
    virtual void StartupModule() override;
    virtual void ShutdownModule() override;

    /** Provides the asset categories supported by this editor module. */
    static EAssetTypeCategories::Type GetInventoryAssetsCategory()
    {
        return InventoryAssetsCategory;
    }
    /** Provides the asset categories supported by this editor module. */
    static EAssetTypeCategories::Type GetCharacterAssetsCategory()
    {
        return CharacterAssetsCategory;
    }
    /** Provides the asset categories supported by this editor module. */
    static EAssetTypeCategories::Type GetAbilityAssetsCategory()
    {
        return AbilityAssetsCategory;
    }

protected:
    void RegisterAssetTypeActions(IAssetTools& AssetTools, const TSharedRef<IAssetTypeActions>& Action)
    {
        AssetTools.RegisterAssetTypeActions(Action);
        CreatedAssetTypeActions.Add(Action);
    }

private:
    /** Main Kuro Paradigm Studio categories. */
    static EAssetTypeCategories::Type InventoryAssetsCategory;
    static EAssetTypeCategories::Type CharacterAssetsCategory;
    static EAssetTypeCategories::Type AbilityAssetsCategory;	
    
    /** All created asset type actions. Cached here so that we can unregister it during shutdown. */
    TArray<TSharedPtr<IAssetTypeActions>> CreatedAssetTypeActions;

    // Keep track of registered detail customizations to unregister them on shutdown
    TArray<FName> RegisteredDetailCustomizations;
    
};