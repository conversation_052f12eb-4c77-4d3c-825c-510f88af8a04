#include "Components/CalamityEncounterComponent.h"

#include "Interfaces/ICalamityEncounterInterface.h" // Include the interface header
#include "GameFramework/Systems/Subsystems/CalamityEncounterManagerSubsystem.h" // Include subsystem header
#include "GameFramework/Actor.h"
#include "GameFramework/PlayerState.h" // Needed if interacting with PlayerState
#include "Engine/World.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h" // Needed for replication
#include "Engine/ActorChannel.h" // Needed for replication
#include "AbilitySystemComponent.h" // Include if using GAS directly
#include "GameplayEffect.h" // Include if using FGameplayEffectSpecHandle directly

#include "CalamityTags.h"

UCalamityEncounterComponent::UCalamityEncounterComponent()
{
    PrimaryComponentTick.bCanEverTick = true; // Enable ticking to update threat levels, decay aggro, etc.
    PrimaryComponentTick.TickGroup = TG_PostPhysics; // Or another appropriate group
    SetIsReplicatedByDefault(true); // Ensure component replicates if the owner does

    // Initialize default values (can also be set in header)
    CurrentAggro = 0.0f;
    CurrentThreatLevel = EThreatLevel::None;
    TimeAboveThreatThreshold = 0.0f;
    CurrentSuppression = 0.0f;
    AggroDecayRate = 5.0f;
    MaxSuppression = 100.0f;
    SuppressionDecayRate = 10.0f;

    // Example Threat Level Thresholds (adjust these values)
    ThreatLevelAggroThresholds.Add(EThreatLevel::Low, 10.0f);
    ThreatLevelAggroThresholds.Add(EThreatLevel::Medium, 35.0f);
    ThreatLevelAggroThresholds.Add(EThreatLevel::High, 65.0f);
    ThreatLevelAggroThresholds.Add(EThreatLevel::Extreme, 85.0f);
    ThreatLevelAggroThresholds.Add(EThreatLevel::Destroyer, 100.0f); // Might require specific conditions

    // Example Sustain Durations (adjust these values)
    ThreatLevelSustainDuration.Add(EThreatLevel::Low, 0.5f);
    ThreatLevelSustainDuration.Add(EThreatLevel::Medium, 2.0f);
    ThreatLevelSustainDuration.Add(EThreatLevel::High, 3.0f);
    ThreatLevelSustainDuration.Add(EThreatLevel::Extreme, 4.0f);
    ThreatLevelSustainDuration.Add(EThreatLevel::Destroyer, 0.0f); // Destroyer might be event-triggered
}

void UCalamityEncounterComponent::BeginPlay()
{
    Super::BeginPlay();
    // Ensure tags are set (e.g., add CalamityTag_Player for players)
    // Cache Owner's Ability System Component if available
    OwnerAbilitySystemComponent = GetOwner()->FindComponentByClass<UAbilitySystemComponent>();

    // Get and cache the Encounter Manager Subsystem
    if (const UGameInstance* GameInstance = GetWorld() ? GetWorld()->GetGameInstance() : nullptr)
    {
        EncounterManagerSubsystem = GameInstance->GetSubsystem<UCalamityEncounterManagerSubsystem>();
    }

    // Register with the subsystem if valid
    if (EncounterManagerSubsystem.IsValid())
    {
        EncounterManagerSubsystem->RegisterEncounterComponent(this);
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("CalamityEncounterComponent: Could not find EncounterManagerSubsystem!"));
    }
}

void UCalamityEncounterComponent::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
	// Unregister from the subsystem
	if (EncounterManagerSubsystem.IsValid())
	{
		EncounterManagerSubsystem->UnregisterEncounterComponent(this);
	}

	Super::EndPlay(EndPlayReason);
}

void UCalamityEncounterComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

	// Only run logic on the server for authoritative state changes
	if (GetOwnerRole() == ROLE_Authority)
	{
		if ((bShouldTick = CurrentAggro > 0.0f || CurrentThreatLevel != EThreatLevel::None))
		{
		DecayAggro(DeltaTime);
		DecaySuppression(DeltaTime);
		UpdateThreatLevel(DeltaTime);
	}
		else
		{
			SetComponentTickEnabled(false);
		}
	}
}

void UCalamityEncounterComponent::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME(UCalamityEncounterComponent, CurrentAggro);
	DOREPLIFETIME(UCalamityEncounterComponent, CurrentThreatLevel);
	DOREPLIFETIME(UCalamityEncounterComponent, ActivePerks);
	// Add replication for CurrentSuppression, CurrentRival if needed visually on clients
}

// --- Internal Logic Implementations ---

void UCalamityEncounterComponent::UpdateThreatLevel(float DeltaTime)
{
	// TODO: Implement logic to check Aggro against thresholds
	// TODO: Track TimeAboveThreatThreshold
	// TODO: Promote/Demote CurrentThreatLevel based on thresholds and sustain duration
	// TODO: Consider specific conditions for Extreme/Destroyer levels
	// TODO: If level changes, update CurrentThreatLevel and broadcast OnThreatLevelChanged delegate
	// Example structure:
	
	/** 1. Determine the highest Threat Level the current Aggro meets */
	EThreatLevel PreviousLevel = CurrentThreatLevel;
	EThreatLevel PotentialNewLevel = EThreatLevel::None;
	float RequiredAggro = 0.0f;

	/** Find the highest level the current aggro meets */
	for (const auto& Pair : ThreatLevelAggroThresholds)
	{
		if (CurrentAggro >= Pair.Value && Pair.Key > PotentialNewLevel)
		{
			PotentialNewLevel = FMath::Max(PotentialNewLevel, Pair.Key);
			RequiredAggro = Pair.Value;
		}
	}

	/** 2. Check if we need to promote/demote
	 * Handle Promotion.
	 */
	if (PotentialNewLevel > CurrentThreatLevel)
	{
		// Check sustain duration if promoting
		const float* SustainDuration = ThreatLevelSustainDuration.Find(PotentialNewLevel);
		if (SustainDuration && *SustainDuration > 0)
		{
			TimeAboveThreatThreshold += DeltaTime;
			if (TimeAboveThreatThreshold >= *SustainDuration)
			{
				CurrentThreatLevel = PotentialNewLevel;
				TimeAboveThreatThreshold = 0.0f; // Reset timer
			}
		}
		else // No sustain needed or instant promotion
		{
			CurrentThreatLevel = PotentialNewLevel;
			TimeAboveThreatThreshold = 0.0f;
		}
	}
	/** 3. Check if we need to demote
	 * Handle Demotion.
	 */
	else if (PotentialNewLevel < CurrentThreatLevel)
	{
		// Demote if aggro drops below the threshold for the *current* level
		const float* CurrentThreshold = ThreatLevelAggroThresholds.Find(CurrentThreatLevel);
		if(CurrentThreshold && CurrentAggro < *CurrentThreshold)
		{
			CurrentThreatLevel = PotentialNewLevel; // Drop down
		}
		// Reset sustain timer if we drop below potential promotion level
		TimeAboveThreatThreshold = 0.0f;
	}
	else
	{
		// Aggro is stable within the current level, reset sustain timer if below potential promotion level
		const float* NextThreshold = ThreatLevelAggroThresholds.Find(static_cast<EThreatLevel>(static_cast<uint8>(CurrentThreatLevel) + 1));
		if(!NextThreshold || CurrentAggro < *NextThreshold)
		{
			TimeAboveThreatThreshold = 0.0f;
		}
	}


	if (PreviousLevel != CurrentThreatLevel)
	{
		OnThreatLevelChanged.Broadcast(CurrentThreatLevel, PreviousLevel);
		// TODO: Apply/Remove passive effects associated with the new level via GAS if applicable
	}
}

void UCalamityEncounterComponent::DecayAggro(float DeltaTime)
{
	// TODO: Implement Aggro decay logic
	// Should only decay if no positive aggro events occurred recently? Or always decay slowly?
	if (CurrentAggro > 0.0f)
	{
		float OldAggro = CurrentAggro;
		CurrentAggro = FMath::Max(0.0f, CurrentAggro - (AggroDecayRate * DeltaTime));
		if (OldAggro != CurrentAggro)
		{
			OnAggroChanged.Broadcast(CurrentAggro);
		}
	}
}

void UCalamityEncounterComponent::DecaySuppression(float DeltaTime)
{
	// TODO: Implement Suppression decay
	if (CurrentSuppression > 0.0f)
	{
		CurrentSuppression = FMath::Max(0.0f, CurrentSuppression - (SuppressionDecayRate * DeltaTime));
		// TODO: Broadcast delegate if needed
	}
}

// --- Replication Callbacks ---

void UCalamityEncounterComponent::OnRep_CurrentAggro()
{
	// Broadcast delegate on clients when aggro value changes
	OnAggroChanged.Broadcast(CurrentAggro);
}

void UCalamityEncounterComponent::OnRep_CurrentThreatLevel()
{
	// Find the old level (requires storing it temporarily or inferring) - COMPLEX
	// For simplicity, maybe just broadcast the new level on clients
	// OnThreatLevelChanged.Broadcast(CurrentThreatLevel, /* How to get old level reliably? */);
	// UE_LOG(LogTemp, Log, TEXT("%s Threat Level Changed to %s on client"), *GetOwner()->GetName(), *UEnum::GetValueAsString(CurrentThreatLevel));
}

void UCalamityEncounterComponent::OnRep_ActivePerks()
{
	// Could compare against a previous state to broadcast individual adds/removes,
	// or just notify that the perk list has changed.
	// UE_LOG(LogTemp, Log, TEXT("%s Active Perks replicated on client"), *GetOwner()->GetName());
}

// --- Interface Implementations ---
 /**
  * Adds aggro to this component, factoring in various gameplay modifiers.
 *
  * - Applies suppression: If the actor is suppressed, aggro gain is reduced.
  * - Applies perk modifiers: Certain perks can increase aggro gain.
  * - Only runs on the server (authority).
  * - Clamps aggro to the 0-100 range.
  * - Broadcasts the new aggro value if changed.
  *
  * @param AggroData The data for the aggro event, including base aggro amount and event context.
 */
void UCalamityEncounterComponent::AddAggro_Implementation(const FAggroEventData& AggroData)
{
	if (GetOwnerRole() == ROLE_Authority)
	{
		float AggroToAdd = AggroData.BaseAggroAmount;
		
		// TODO: Apply modifiers based on EventTags, Perks, TargetActor state, Suppression, Rival status etc.
		// Example: Reduce aggro gain if suppressed
		/**Apply suppression modifier*/
		if (CurrentSuppression > 50.0f) // Example threshold
		{
			float SuppressionModifier = 1.0f - (CurrentSuppression / MaxSuppression);
			AggroToAdd *= FMath::Clamp(SuppressionModifier, 0.1f, 1.0f);
			// AggroToAdd *= 0.75f; // Reduce gain by 25%
		}

		// Apply perk modifiers
		for (const FGameplayTag& Perk : ActivePerks)
		{
			// Check for aggro modification perks
			if (Perk.MatchesTag(FGameplayTag::RequestGameplayTag("Perk.Aggro.Increase")))
			{
				AggroToAdd *= 1.25f;
			}
		}
		
		if (AggroToAdd != 0.0f)
		{
			CurrentAggro = FMath::Clamp(CurrentAggro + AggroToAdd, 0.0f, 100.0f); // Clamp to 0-100 range
			OnAggroChanged.Broadcast(CurrentAggro);
			UE_LOG(LogTemp, Log, TEXT("%s Aggro changed to %f"), *GetOwner()->GetName(), CurrentAggro);
		}
	}
}

float UCalamityEncounterComponent::GetCurrentAggro_Implementation() const
{
	return CurrentAggro;
}

EThreatLevel UCalamityEncounterComponent::GetCurrentThreatLevel_Implementation() const
{
	return CurrentThreatLevel;
}

void UCalamityEncounterComponent::AddPerk_Implementation(const FGameplayTag& PerkTag)
{
	if (GetOwnerRole() == ROLE_Authority)
	{
		if (!ActivePerks.HasTag(PerkTag))
		{
			ActivePerks.AddTag(PerkTag);
			OnPerkAdded.Broadcast(PerkTag);
			// Force net update if needed, although container replication should handle it
			// ForceNetUpdate();
		}
	}
}

void UCalamityEncounterComponent::RemovePerk_Implementation(const FGameplayTag& PerkTag)
{
	if (GetOwnerRole() == ROLE_Authority)
	{
		if (ActivePerks.HasTag(PerkTag))
		{
			ActivePerks.RemoveTag(PerkTag);
			OnPerkRemoved.Broadcast(PerkTag);
			// ForceNetUpdate();
		}
	}
}

bool UCalamityEncounterComponent::HasPerk_Implementation(const FGameplayTag& PerkTag) const
{
	return ActivePerks.HasTag(PerkTag);
}

FGameplayTagContainer UCalamityEncounterComponent::GetActivePerks_Implementation() const
{
	return ActivePerks;
}

void UCalamityEncounterComponent::ApplyEncounterEffect_Implementation(const FGameplayEffectSpecHandle& EffectSpecHandle)
{
	// Example implementation using GAS
	if (OwnerAbilitySystemComponent.IsValid())
	{
		OwnerAbilitySystemComponent->ApplyGameplayEffectSpecToSelf(*EffectSpecHandle.Data.Get());
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("ApplyEncounterEffect called on %s but OwnerAbilitySystemComponent is invalid!"), *GetOwner()->GetName());
	}
}

void UCalamityEncounterComponent::NotifyEncounterEvent_Implementation(const FGameplayTag& EventTag, AActor* Instigator)
{
	// TODO: Handle specific encounter events relevant to this component/player
	// e.g., Play specific VO, trigger local screen effect, apply a short buff/debuff via GAS
	UE_LOG(LogTemp, Log, TEXT("%s notified of Encounter Event: %s from %s"),
		*GetOwner()->GetName(),
		*EventTag.ToString(),
		Instigator ? *Instigator->GetName() : TEXT("None"));

	// Example: If Tank Duel Won, maybe apply a temporary "Enraged" effect
	// if(EventTag == FGameplayTag::RequestGameplayTag(FName("Encounter.Event.TankDuelWon"))) { // Apply Effect }
}

void UCalamityEncounterComponent::NotifySuppression_Implementation(AActor* Suppressor, float SuppressionAmount)
{
	if (GetOwnerRole() == ROLE_Authority)
	{
		CurrentSuppression = FMath::Clamp(CurrentSuppression + SuppressionAmount, 0.0f, MaxSuppression);
		// TODO: Broadcast delegate if needed
		// UE_LOG(LogTemp, Log, TEXT("%s Suppression changed to %f by %s"), *GetOwner()->GetName(), CurrentSuppression, *Suppressor->GetName());
	}
}

void UCalamityEncounterComponent::UpdateRivalStatus_Implementation(AActor* RivalActor, bool bIsNowRival)
{
	if (GetOwnerRole() == ROLE_Authority)
	{
		CurrentRival = bIsNowRival ? RivalActor : nullptr;
		// TODO: Potentially apply passive effects or change aggro modifiers when a rival is active/nearby
		// UE_LOG(LogTemp, Log, TEXT("%s Rival status updated: %s"), *GetOwner()->GetName(), bIsNowRival ? *RivalActor->GetName() : TEXT("None"));
	}
}

void UCalamityEncounterComponent::OnEncounterStarted_Implementation(bool bIsOneVsOne,const TArray<AActor*>& Participants)
{
	ICalamityEncounterInterface::OnEncounterStarted_Implementation(bIsOneVsOne, Participants);
	// Play sounds, trigger UI, potentially apply initial encounter effects
	UE_LOG(LogTemp, Log, TEXT("%s Encounter Started! 1v1: %s"), *GetOwner()->GetName(), bIsOneVsOne ? TEXT("Yes") : TEXT("No"));
}

void UCalamityEncounterComponent::OnEncounterEnded_Implementation()
{
	ICalamityEncounterInterface::OnEncounterEnded_Implementation();
	// Clean up effects, stop sounds, update UI
	UE_LOG(LogTemp, Log, TEXT("%s Encounter Ended!"), *GetOwner()->GetName());
}

// --- Helper Functions ---

UAbilitySystemComponent* UCalamityEncounterComponent::GetOwnerAbilitySystemComponent() const
{
	return OwnerAbilitySystemComponent.Get();
}

void UCalamityEncounterComponent::DebugDrawEncounterInfo(float DeltaTime)
{
}

void UCalamityEncounterComponent::ToggleDebugDraw()
{
}
