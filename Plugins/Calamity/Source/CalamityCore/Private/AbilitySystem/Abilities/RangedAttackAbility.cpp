// RangedAttackAbility.cpp
#include "AbilitySystem/Abilities/RangedAttackAbility.h"
#include "GameFramework/Character.h"
#include "Abilities/Tasks/AbilityTask_PlayMontageAndWait.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetMathLibrary.h"
#include "GameFramework/ProjectileMovementComponent.h"
#include "Components/SphereComponent.h"
#include "GameplayEffect.h"
#include "AbilitySystem/Components/CalamityAbilitySystemComponent.h"

URangedAttackAbility::URangedAttackAbility()
{
    // Default values
    BaseDamage = 15.0f;
    ProjectileSpeed = 2000.0f;
    bIsHoming = false;
    HomingStrength = 0.5f;
    ProjectileCount = 1;
    SpreadAngle = 5.0f;

    // Set defaults for the base class
    AbilityInputID = ECalamityAbilityInputID::RangedAttack;

    // Can be used with both power types by default
    PowerType = EAbilityPowerType::Universal;

    // Style points
    BaseStylePoints = 15;

    // Energy cost
    EnergyCost = 10.0f;

    // Cooldown settings
    //CooldownTag = FGameplayTag::RequestGameplayTag("Ability.Ranged.Cooldown");
    CooldownDuration = 0.5f;

    // Set instancing policy
    InstancingPolicy = EGameplayAbilityInstancingPolicy::InstancedPerActor;
}

void URangedAttackAbility::ActivateAbility(const FGameplayAbilitySpecHandle Handle,
                                         const FGameplayAbilityActorInfo* ActorInfo,
                                         const FGameplayAbilityActivationInfo ActivationInfo,
                                         const FGameplayEventData* TriggerEventData)
{
    // Commit the ability cost
    if (!CommitAbility(Handle, ActorInfo, ActivationInfo))
    {
        EndAbility(Handle, ActorInfo, ActivationInfo, true, true);
        return;
    }

    // Call parent activate ability
    Super::ActivateAbility(Handle, ActorInfo, ActivationInfo, TriggerEventData);

    // Get the character
    ACharacter* Character = Cast<ACharacter>(ActorInfo->AvatarActor.Get());
    if (!Character)
    {
        EndAbility(Handle, ActorInfo, ActivationInfo, true, true);
        return;
    }

    // Play fire montage if available
    if (FireMontage)
    {
        UAbilityTask_PlayMontageAndWait* MontageTask = UAbilityTask_PlayMontageAndWait::CreatePlayMontageAndWaitProxy(
            this,
            NAME_None,
            FireMontage,
            1.0f,
            NAME_None,
            false
        );

        if (MontageTask)
        {
            // Fix the delegate connection - this needs to match the function signature
            MontageTask->OnCompleted.AddDynamic(this, &URangedAttackAbility::OnMontageCompleted);
            MontageTask->ReadyForActivation();
        }
        else
        {
            // If montage task creation failed, just fire immediately
            FireProjectile();
        }
    }
    else
    {
        // No montage, just fire immediately
        FireProjectile();
    }

    // Apply ability cost and cooldown
    ApplyCooldown(Handle, ActorInfo, ActivationInfo);
}

void URangedAttackAbility::OnMontageCompleted()
{
    // Fire the projectile when the montage completes
    FireProjectile();

    // End the ability
    EndAbility(CurrentSpecHandle, CurrentActorInfo, CurrentActivationInfo, true, false);
}

void URangedAttackAbility::FireProjectile()
{
    if (!ProjectileClass)
    {
        EndAbility(CurrentSpecHandle, CurrentActorInfo, CurrentActivationInfo, true, true);
        return;
    }

    ACharacter* Character = Cast<ACharacter>(GetAvatarActorFromActorInfo());
    if (!Character)
    {
        EndAbility(CurrentSpecHandle, CurrentActorInfo, CurrentActivationInfo, true, true);
        return;
    }

    // Calculate spawn position (in front of the character)
    FVector SpawnLocation = Character->GetActorLocation() + (Character->GetActorForwardVector() * 50.0f);
    SpawnLocation.Z += 50.0f; // Offset to spawn at chest height

    // Get the character's controller rotation (where they're aiming)
    FRotator SpawnRotation = Character->GetControlRotation();

    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
    SpawnParams.Owner = Character;
    SpawnParams.Instigator = Character;

    // Fire multiple projectiles if ProjectileCount > 1
    for (int32 i = 0; i < ProjectileCount; i++)
    {
        FRotator ProjectileRotation = SpawnRotation;

        // Apply spread for multiple projectiles
        if (ProjectileCount > 1)
        {
            // Calculate spread angle
            float SpreadX = FMath::RandRange(-SpreadAngle, SpreadAngle);
            float SpreadY = FMath::RandRange(-SpreadAngle, SpreadAngle);

            ProjectileRotation.Pitch += SpreadX;
            ProjectileRotation.Yaw += SpreadY;
        }

        // Spawn the projectile
        AActor* Projectile = GetWorld()->SpawnActor<AActor>(
            ProjectileClass,
            SpawnLocation,
            ProjectileRotation,
            SpawnParams
        );

        if (Projectile)
        {
            // Configure projectile movement component if it exists
            UProjectileMovementComponent* ProjectileMovement = Cast<UProjectileMovementComponent>(
                Projectile->GetComponentByClass(UProjectileMovementComponent::StaticClass())
            );

            if (ProjectileMovement)
            {
                ProjectileMovement->InitialSpeed = ProjectileSpeed;
                ProjectileMovement->MaxSpeed = ProjectileSpeed;

                // Configure homing if enabled
                if (bIsHoming)
                {
                    ProjectileMovement->bIsHomingProjectile = true;
                    ProjectileMovement->HomingAccelerationMagnitude = HomingStrength * 10000.0f;

                    // Try to find a target for homing
                    AActor* HomingTarget = FindHomingTarget(Character);
                    if (HomingTarget)
                    {
                        ProjectileMovement->HomingTargetComponent = HomingTarget->GetRootComponent();
                    }
                }
            }

            // Setup projectile damage
            SetupProjectileDamage(Projectile);
        }
    }
}

AActor* URangedAttackAbility::FindHomingTarget(ACharacter* Character)
{
    // Simple implementation to find the closest enemy in front of the character
    if (!Character)
    {
        return nullptr;
    }

    TArray<AActor*> FoundActors;
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), APawn::StaticClass(), FoundActors);

    AActor* ClosestTarget = nullptr;
    float ClosestDistance = 5000.0f; // Max homing range

    for (AActor* Actor : FoundActors)
    {
        // Skip the character itself
        if (Actor == Character)
        {
            continue;
        }

        // Check if the actor is an enemy (you'd need your own logic for this)
        // Replace this with your actual enemy detection logic
        bool bIsEnemy = true; // Simplified for now

        if (bIsEnemy)
        {
            // Check if target is in front of the character
            FVector DirectionToTarget = Actor->GetActorLocation() - Character->GetActorLocation();
            DirectionToTarget.Normalize();

            float DotProduct = FVector::DotProduct(Character->GetActorForwardVector(), DirectionToTarget);

            if (DotProduct > 0.5f) // Target is roughly in front of the character
            {
                float Distance = FVector::Dist(Character->GetActorLocation(), Actor->GetActorLocation());

                if (Distance < ClosestDistance)
                {
                    ClosestDistance = Distance;
                    ClosestTarget = Actor;
                }
            }
        }
    }

    return ClosestTarget;
}

void URangedAttackAbility::SetupProjectileDamage(AActor* Projectile)
{
    if (!Projectile)
    {
        return;
    }

    // Store damage value in the projectile
    Projectile->Tags.Add(FName(*FString::Printf(TEXT("Damage:%f"), BaseDamage)));

    // Add owner to projectile
    Projectile->Tags.Add(FName(*FString::Printf(TEXT("Owner:%s"), *GetAvatarActorFromActorInfo()->GetName())));

    // Find collision component
    USphereComponent* SphereCollision = Cast<USphereComponent>(
        Projectile->GetComponentByClass(USphereComponent::StaticClass())
    );

    if (SphereCollision)
    {
        // Set up collision handler for the projectile component
        SphereCollision->OnComponentBeginOverlap.AddDynamic(this, &URangedAttackAbility::OnProjectileOverlap);
    }
}

void URangedAttackAbility::OnProjectileOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor,
                                            UPrimitiveComponent* OtherComp, int32 OtherBodyIndex,
                                            bool bFromSweep, const FHitResult& SweepResult)
{
    // Don't hit the ability owner
    if (OtherActor == GetAvatarActorFromActorInfo())
    {
        return;
    }

    // Get projectile actor
    AActor* ProjectileActor = OverlappedComponent->GetOwner();
    if (!ProjectileActor)
    {
        return;
    }

    // Get damage from projectile tag
    float Damage = BaseDamage;
    for (const FName& Tag : ProjectileActor->Tags)
    {
        FString TagString = Tag.ToString();
        if (TagString.StartsWith("Damage:"))
        {
            FString DamageString = TagString.RightChop(7); // Skip "Damage:"
            Damage = FCString::Atof(*DamageString);
            break;
        }
    }

    // Apply damage to the hit actor
    UGameplayStatics::ApplyDamage(
        OtherActor,
        Damage,
        GetAvatarActorFromActorInfo()->GetInstigatorController(),
        GetAvatarActorFromActorInfo(),
        UDamageType::StaticClass()
    );

    // Destroy the projectile
    ProjectileActor->Destroy();
}