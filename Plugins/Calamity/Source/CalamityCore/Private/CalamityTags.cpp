// Copyright Calamity Inc. All Rights Reserved.

#include "CalamityTags.h"

namespace CalamityTags
{
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(None, "None", "No tag");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(All, "All", "All tags");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Any, "Any", "Any tag");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(AnyContext, "AnyContext", "Any context");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(AnyContext_Weapon, "AnyContext.Weapon", "Any weapon context");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(AnyContext_Weapon_Ranged, "AnyContext.Weapon.Ranged", "Any ranged weapon context");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(AnyContext_Weapon_Melee, "AnyContext.Weapon.Melee", "Any melee weapon context");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Team, "Team", "Base tag for all teams");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Team_All, "Team.All", "All teams");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Team_A, "Team.A", "Team A");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Team_B, "Team.B", "Team B");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Team_C, "Team.C", "Team C");
	UE_DEFINE_GAMEPLAY_TAG_COMMENT(Team_D, "Team.D", "Team D");
	
	namespace ActionTags
	{
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Action, "Action", "Base tag for all actions");
	}

	namespace AbilityTags
	{
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Ability, "Ability", "Base tag for all abilities");
        
		// Telekinesis Tags
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Ability_Telekinesis, "Ability.Telekinesis", "Base tag for telekinesis abilities");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Ability_Telekinesis_Active, "Ability.Telekinesis.Active", "Telekinesis ability is active");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Ability_Telekinesis_Cooldown, "Ability.Telekinesis.Cooldown", "Telekinesis ability is on cooldown");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Ability_Telekinesis_GrabThrow, "Ability.Telekinesis.GrabThrow", "Telekinetic grab and throw ability");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Ability_Telekinesis_Grabbing, "Ability.Telekinesis.Grabbing", "Currently grabbing with telekinesis");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Ability_Telekinesis_Holding, "Ability.Telekinesis.Holding", "Currently holding with telekinesis");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Ability_Telekinesis_Throwing, "Ability.Telekinesis.Throwing", "Currently throwing with telekinesis");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Ability_Telekinesis_Pull, "Ability.Telekinesis.Pull", "Telekinetic pull ability");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Ability_Telekinesis_Pull_Active, "Ability.Telekinesis.Pull.Active", "Telekinetic pull is active");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Ability_Telekinesis_Pull_Cooldown, "Ability.Telekinesis.Pull.Cooldown", "Telekinetic pull is on cooldown");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Ability_Telekinesis_Pull_Effect, "Ability.Telekinesis.Pull.Effect", "Effect applied by telekinetic pull");
	}

	namespace StateTags
	{
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(State, "State", "Base tag for all character states");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_Idle, "State.Idle", "Character is in idle state");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_Moving, "State.Moving", "Character is moving");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_Jumping, "State.Jumping", "Character is jumping");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_Attacking, "State.Attacking", "Character is performing an attack");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_Attacked, "State.Attacked", "Character is being attacked");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_Blocking, "State.Blocking", "Character is blocking");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_Dashing, "State.Dashing", "Character is dashing");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_Stunned, "State.Stunned", "Character is stunned");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_Alive, "State.Alive", "Character is alive");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_Dead, "State.Dead", "Character is dead");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_Grabbing, "State.Grabbing", "Character is grabbing something");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_Grabbed, "State.Grabbed", "Character is being grabbed");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_Holding, "State.Holding", "Character is holding something");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_Held, "State.Held", "Character is being held");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_Throwing, "State.Throwing", "Character is throwing something");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_Thrown, "State.Thrown", "Character is being thrown");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_Climbing, "State.Climbing", "Character is climbing");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_Consuming, "State.Consuming", "Character is consuming an item");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_Consumed, "State.Consumed", "Character is being consumed");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_TelekineticGrabbed, "State.TelekineticGrabbed", "Character is grabbed by telekinesis");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(State_TelekineticPulling, "State.TelekineticPulling", "Character is being pulled by telekinesis");
	}
	
	namespace EffectTags
	{
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Effect, "Effect", "Base tag for all effects");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Effect_Damage, "Effect.Damage", "Base tag for damage effects");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Effect_Damage_Blocked, "Effect.Damage.Blocked", "Damage was blocked");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Effect_Damage_Critical, "Effect.Damage.Critical", "Critical damage hit");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Effect_Damage_Physical, "Effect.Damage.Physical", "Physical damage type");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Effect_Damage_Rift, "Effect.Damage.Rift", "Rift damage type");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Effect_Damage_Radiance, "Effect.Damage.Radiance", "Radiance damage type");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Effect_Damage_Fire, "Effect.Damage.Fire", "Fire damage type");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Effect_Damage_Ice, "Effect.Damage.Ice", "Ice damage type");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Effect_Damage_Lightning, "Effect.Damage.Lightning", "Lightning damage type");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Effect_Damage_Poison, "Effect.Damage.Poison", "Poison damage type");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Effect_Damage_Melee, "Effect.Damage.Melee", "Melee damage source");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Effect_Damage_Ranged, "Effect.Damage.Ranged", "Ranged damage source");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Effect_Damage_Mitigated, "Effect.Damage.Mitigated", "Damage was reduced");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Effect_Damage_Recurring, "Effect.Damage.Recurring", "Damage over time effect");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Effect_Damage_Stagger, "Effect.Damage.Stagger", "Damage causes stagger effect");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Effect_Movement_Pulled, "Effect.Movement.Pulled", "Target is being pulled");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Effect_Damage_Knockback, "Effect.Damage.Knockback", "Damage causes knockback");
		
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(StatusEffect, "StatusEffect", "Base tag for status effects");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(StatusEffect_Bleeding, "StatusEffect.Bleeding", "Target is bleeding");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(StatusEffect_Burning, "StatusEffect.Burning", "Target is burning");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(StatusEffect_Frozen, "StatusEffect.Frozen", "Target is frozen");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(StatusEffect_Shocked, "StatusEffect.Shocked", "Target is shocked");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(StatusEffect_Poisoned, "StatusEffect.Poisoned", "Target is poisoned");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(StatusEffect_Stunned, "StatusEffect.Stunned", "Target is stunned");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(StatusEffect_Immobilized, "StatusEffect.Immobilized", "Target cannot move");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(StatusEffect_Weakened, "StatusEffect.Weakened", "Target's damage is reduced");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(StatusEffect_Enraged, "StatusEffect.Enraged", "Target's damage is increased");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(StatusEffect_Hasted, "StatusEffect.Hasted", "Target's speed is increased");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(StatusEffect_Healed, "StatusEffect.Healed", "Target is being healed");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(StatusEffect_Movement, "StatusEffect.Movement", "Base tag for movement effects");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(StatusEffect_Movement_Slowed, "StatusEffect.Movement.Slowed", "Target's movement is slowed");
	}

	namespace ElementTags
	{
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Element, "Element", "Base tag for all elemental types");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Element_None, "Element.None", "No elemental type");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Element_Rift, "Element.Rift", "Rift elemental type");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Element_Radiance, "Element.Radiance", "Radiance elemental type");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Element_Fire, "Element.Fire", "Fire elemental type");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Element_Ice, "Element.Ice", "Ice elemental type");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Element_Lightning, "Element.Lightning", "Lightning elemental type");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Element_Wind, "Element.Wind", "Wind elemental type");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Element_Earth, "Element.Earth", "Earth elemental type");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Element_Water, "Element.Water", "Water elemental type");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Element_Light, "Element.Light", "Light elemental type");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Element_Dark, "Element.Dark", "Dark elemental type");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Element_Physical, "Element.Physical", "Physical elemental type");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Element_Wood, "Element.Wood", "Wood elemental type");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Element_Metal, "Element.Metal", "Metal elemental type");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Element_Spirit, "Element.Spirit", "Spirit elemental type");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Element_Energy, "Element.Energy", "Energy elemental type");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Element_Acid, "Element.Acid", "Acid elemental type");
	}

	namespace EncounterTags
	{
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Encounter, "Encounter", "Base tag for all encounters");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Encounter_OutCome_Won, "Encounter.OutCome.Won", "Encounter Won");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Encounter_OutCome_Loss, "Encounter.OutCome.Loss", "Encounter Loss");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Encounter_Player, "Encounter.Player", "Player encounter");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Encounter_Enemy, "Encounter.Enemy", "Enemy encounter");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Encounter_Ally, "Encounter.Ally", "Ally encounter");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Encounter_Objective, "Encounter.Objective", "Objective encounter");
	}
	
	namespace EnvironmentTags
	{
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Environment, "Environment", "Base tag for all environment related tags");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Environment_Outdoors, "Environment.Outdoors", "Base tag for all Outdoor Environments related tags");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Environment_Indoors, "Environment.Indoors", "Base tag for all Indoor Environments related tags");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Environment_Day, "Environment.Day", "Daytime environment");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Environment_Night, "Environment.Night", "Nighttime environment");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Environment_Zone, "Environment.Zone", "Base tag for all environment zones");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Environment_Zone_Outdoor, "Environment.Zone.Outdoor", "Outdoor environment zone");
	}
	
	namespace InputTags
	{
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Input, "Input", "Base tag for all input related tags");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Input_Move, "Input.Move", "Movement input");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Input_Look, "Input.Look", "Look/camera input");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Input_MoveForward, "Input.MoveForward", "Forward movement input");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Input_MoveRight, "Input.MoveRight", "Right movement input");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Input_Jump, "Input.Jump", "Jump input");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Input_Dash, "Input.Dash", "Dash input");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Input_Sprint, "Input.Sprint", "Sprint input");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Input_Crouch, "Input.Crouch", "Crouch input");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Input_Strafe, "Input.Strafe", "Strafe input");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Input_Interact, "Input.Interact", "Interaction input");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Input_Inventory, "Input.Inventory", "Inventory input");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Input_NextWeapon, "Input.NextWeapon", "Next weapon selection input");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Input_PrevWeapon, "Input.PrevWeapon", "Previous weapon selection input");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Input_Reload, "Input.Reload", "Reload input");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Input_Grab, "Input.Grab", "Grab input");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Input_Consume, "Input.Consume", "Consume item input");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Input_Throw, "Input.Throw", "Throw input");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Input_Slam, "Input.Slam", "Slam input");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Input_Attack, "Input.Attack", "Attack input");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Input_Block, "Input.Block", "Block input");
	}
	
	namespace CombatTags
	{
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Combat, "Combat", "Base tag for all combat related tags");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Combat_CanAttack, "Combat.CanAttack", "Character can perform attacks");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Combat_CanBlock, "Combat.CanBlock", "Character can block");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Combat_CanDash, "Combat.CanDash", "Character can dash");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Combat_Component_WeaponTrail, "Combat.Component.WeaponTrail", "Mesh used for weapon trails");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Combat_Component_ProjectileSource, "Combat.Component.ProjectileSource", "Mesh used for Spawning Projectiles");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Combat_Component_MeleeScanSource, "Combat.Component.MeleeScanSource", "Mesh used for Melee Scan Source");
	}

	namespace MovementTags
	{
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Movement, "Movement", "Base tag for all movement related tags");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Movement_CanMove, "Movement.CanMove", "Character can move");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Movement_CanJump, "Movement.CanJump", "Character can jump");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Movement_CanSprint, "Movement.CanSprint", "Character can sprint");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Movement_CanDash, "Movement.CanDash", "Character can dash");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Movement_CanClimb, "Movement.CanClimb", "Character can climb");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Movement_CanWallRun, "Movement.CanWallRun", "Character can wall run");
	}

	namespace AbilityActivationTags
	{
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(AbilityActivation, "AbilityActivation", "Base tag for ability activation states");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(AbilityActivation_Activated, "AbilityActivation.Activated", "Ability has been successfully activated");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(AbilityActivation_Cancelled, "AbilityActivation.Cancelled", "Ability activation was cancelled");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(AbilityActivation_Failed, "AbilityActivation.Failed", "Base tag for ability activation failures");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(AbilityActivation_Failed_IsDead, "AbilityActivation.Failed.IsDead", "Ability failed because character is dead");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(AbilityActivation_Failed_Cooldown, "AbilityActivation.Failed.Cooldown", "Ability failed because it's on cooldown");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(AbilityActivation_Failed_Cost, "AbilityActivation.Failed.Cost", "Ability failed because cost requirements not met");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(AbilityActivation_Failed_TagsBlocked, "AbilityActivation.Failed.TagsBlocked", "Ability failed because required tags are blocked");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(AbilityActivation_Failed_TagsMissing, "AbilityActivation.Failed.TagsMissing", "Ability failed because required tags are missing");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(AbilityActivation_Failed_Networking, "AbilityActivation.Failed.Networking", "Ability failed due to networking issues");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(AbilityActivation_Failed_ActivationGroup, "AbilityActivation.Failed.ActivationGroup", "Ability failed due to activation group restrictions");
	}

	namespace AbilityTraitTags
	{
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(AbilityTrait, "AbilityTrait", "Base tag for ability traits");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(AbilityTrait_Active, "AbilityTrait.Active", "Ability is currently active");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(AbilityTrait_ActivationOnSpawn, "AbilityTrait.ActivationOnSpawn", "Ability activates when character spawns");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(AbilityTrait_Persistent, "AbilityTrait.Persistent", "Ability remains active until explicitly deactivated");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(AbilityTrait_Stackable, "AbilityTrait.Stackable", "Ability can stack multiple instances");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(AbilityTrait_Stackable_Stacking, "AbilityTrait.Stackable.Stacking", "Ability is currently stacking");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(AbilityTrait_Cooldown, "AbilityTrait.Cooldown", "Ability has cooldown mechanics");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(AbilityTrait_Stackable_Stacking_Cost, "AbilityTrait.Stackable.Stacking.Cost", "Ability has stacking cost mechanics");
	}

	namespace CharacterTags
	{
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Character, "Character", "Base tag for all character types");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Character_Player, "Character.Player", "Base tag for player characters");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Character_Player_Hero, "Character.Player.Hero", "Main player character/hero");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Character_Ai, "Character.Ai", "Base tag for AI-controlled characters");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Character_Ally, "Character.Ally", "Friendly NPCs that assist the player");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Character_Vendor, "Character.Vendor", "NPCs that provide trading services");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Character_NonPlayer, "Character.NonPlayer", "Base tag for all non-player characters");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Character_NonPlayer_Ai, "Character.NonPlayer.Ai", "AI-controlled non-player characters");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Character_Enemy, "Character.Enemy", "Base tag for enemy characters");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Character_Enemy_Leader, "Character.Enemy.Leader", "Enemy squad leader");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Character_Enemy_SquadMember, "Character.Enemy.SquadMember", "Member of an enemy squad");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Character_Enemy_Fodder, "Character.Enemy.Fodder", "Basic weak enemy type");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Character_Enemy_Light, "Character.Enemy.Light", "Light-class enemy");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Character_Enemy_Medium, "Character.Enemy.Medium", "Medium-class enemy");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Character_Enemy_Heavy, "Character.Enemy.Heavy", "Heavy-class enemy");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Character_Enemy_Elite, "Character.Enemy.Elite", "Elite-class enemy");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Character_Enemy_Boss, "Character.Enemy.Boss", "Boss-class enemy");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Character_Enemy_Summoned, "Character.Enemy.Summoned", "Enemy summoned during combat");
	}

	namespace ItemTags
	{
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Container, "Inventory.Container", "Tag for all Container items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Container_Item, "Inventory.Container.Weapon.Item", "Tag for all Container Weapons items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Container_Weapon, "Inventory.Container.Weapon", "Tag for all Container Weapons items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Container_Weapon_Primary, "Inventory.Container.Weapon.Primary", "Tag for all Container Primary Weapon items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Container_Weapon_Primary_A, "Inventory.Container.Weapon.Primary.A", "Tag for all Container Primary Weapon items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Container_Weapon_Primary_B, "Inventory.Container.Weapon.Primary.B", "Tag for all Container Primary Weapon items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Container_Weapon_Secondary, "Inventory.Container.Weapon.Secondary", "Tag for all Container Secondary Weapon items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Container_Weapon_Secondary_A, "Inventory.Container.Weapon.Secondary.A", "Tag for all Container Secondary Weapon items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Container_Weapon_Secondary_B, "Inventory.Container.Weapon.Secondary.B", "Tag for all Container Secondary Weapon items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Container_Wearable, "Inventory.Container.Wearable", "Tag for all Container Wearable items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Container_Armor, "Inventory.Container.Armor", "Tag for all Container Armor items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Container_Consumable, "Inventory.Container.Consumable", "Tag for all Container Consumable items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Container_Attachment, "Inventory.Container.Attachment", "Tag for all Container Attachment items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Container_Cosmetic, "Inventory.Container.Cosmetic", "Tag for all Container Cosmetic items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Container_Misc, "Inventory.Container.Misc", "Tag for all Container Misc items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Container_Backpack, "Inventory.Container.Backpack", "Tag for all Container Backpack items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Container_Chest, "Inventory.Container.Chest", "Tag for all Container Chest items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Container_Chest_Small, "Inventory.Container.Chest.Small", "Tag for all Container Small Chest items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Container_Chest_Medium, "Inventory.Container.Chest.Medium", "Tag for all Container Medium Chest items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Container_Chest_Large, "Inventory.Container.Chest.Large", "Tag for all Container Large Chest items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Container_Chest_XLarge, "Inventory.Container.Chest.XLarge", "Tag for all Container Very Large Chest items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Container_Bag, "Inventory.Container.Bag", "Tag for all Container Bag items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Container_Bag_Small, "Inventory.Container.Bag.Small", "Tag for all Container Small Bag items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Container_Bag_Medium, "Inventory.Container.Bag.Medium", "Tag for all Container Medium Bag items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Container_Bag_Large, "Inventory.Container.Bag.Large", "Tag for all Container Large Bag items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Container_Bag_XLarge, "Inventory.Container.Bag.XLarge", "Tag for all Container Very Large Bag items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Container_Currency, "Inventory.Container.Currency", "Tag for all Container Currency items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Container_LootBox, "Inventory.Container.LootBox", "Tag for all Container LootBox items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Container_Quest, "Inventory.Container.Quest", "Tag for all Container Quest items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Container_Vendor, "Inventory.Container.Vendor", "Tag for all Container Vendor items");

		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item, "Inventory.Item", "Base tag for all items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Pickupable, "Inventory.Item.Pickupable", "Tag for all Pickupable items");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon, "Inventory.Item.Weapon", "Base tag for all weapons");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Ranged, "Inventory.Item.Weapon.Ranged", "Base tag for ranged weapons");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Ranged_Bow, "Inventory.Item.Weapon.Ranged.Bow", "Bow weapon");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Ranged_Crossbow, "Inventory.Item.Weapon.Ranged.Crossbow", "Crossbow weapon");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Ranged_Rifle, "Inventory.Item.Weapon.Ranged.Rifle", "Rifle weapon");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Ranged_Pistol, "Inventory.Item.Weapon.Ranged.Pistol", "Pistol weapon");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Ranged_Shotgun, "Inventory.Item.Weapon.Ranged.Shotgun", "Shotgun weapon");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Ranged_MachineGun, "Inventory.Item.Weapon.Ranged.MachineGun", "Machine gun weapon");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Ranged_Sniper, "Inventory.Item.Weapon.Ranged.Sniper", "Sniper weapon");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Ranged_Cannon, "Inventory.Item.Weapon.Ranged.Cannon", "Cannon weapon");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Ranged_Launcher, "Inventory.Item.Weapon.Ranged.Launcher", "Launcher weapon");
	
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Melee, "Inventory.Item.Weapon.Melee", "Base tag for melee weapons");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Melee_OneHanded, "Inventory.Item.Weapon.Melee.OneHanded", "One-handed melee weapon");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Melee_TwoHanded, "Inventory.Item.Weapon.Melee.TwoHanded", "Two-handed melee weapon");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Melee_Thrown, "Inventory.Item.Weapon.Melee.Thrown", "Thrown melee weapon");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Melee_Staff, "Inventory.Item.Weapon.Melee.Staff", "Staff weapon");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Melee_Shield, "Inventory.Item.Weapon.Melee.Shield", "Shield");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Melee_Gauntlet, "Inventory.Item.Weapon.Melee.Gauntlet", "Gauntlet weapon");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Melee_Polearm, "Inventory.Item.Weapon.Melee.Polearm", "Polearm weapon");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Melee_Dagger, "Inventory.Item.Weapon.Melee.Dagger", "Dagger weapon");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Melee_Sword, "Inventory.Item.Weapon.Melee.Sword", "Sword weapon");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Melee_Axe, "Inventory.Item.Weapon.Melee.Axe", "Axe weapon");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Melee_Mace, "Inventory.Item.Weapon.Melee.Mace", "Mace weapon");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Melee_Spear, "Inventory.Item.Weapon.Melee.Spear", "Spear weapon");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Melee_Fist, "Inventory.Item.Weapon.Melee.Fist", "Fist weapon");

		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Magic, "Inventory.Item.Weapon.Magic", "Base tag for magic weapons");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Magic_Staff, "Inventory.Item.Weapon.Magic.Staff", "Magic staff");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Magic_Wand, "Inventory.Item.Weapon.Magic.Wand", "Magic wand");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Magic_Orb, "Inventory.Item.Weapon.Magic.Orb", "Magic orb");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Magic_Tome, "Inventory.Item.Weapon.Magic.Tome", "Magic tome");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Magic_Rune, "Inventory.Item.Weapon.Magic.Rune", "Magic rune");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Magic_Amulet, "Inventory.Item.Weapon.Magic.Amulet", "Magic amulet");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Magic_Relic, "Inventory.Item.Weapon.Magic.Relic", "Magic relic");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Magic_Special, "Inventory.Item.Weapon.Magic.Special", "Special magic weapon");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Weapon_Magic_Hand, "Inventory.Item.Weapon.Magic.Hand", "Magic hand weapon");

		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Consumable, "Inventory.Item.Consumable", "Consumable item");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Armor, "Inventory.Item.Armor", "Armor item");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Inventory_Item_Attachment, "Inventory.Item.Attachment", "Attachment item");
	}

	namespace EquipmentTags
	{
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot, "Equipment.Slot", "Base tag for all equipment slots");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_FloatingSocket, "Equipment.Slot.FloatingSocket", "Floating equipment slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon, "Equipment.Slot.Weapon", "Weapon equipment slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_MainHand, "Equipment.Slot.Weapon.MainHand", "Main hand weapon slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_OffHand, "Equipment.Slot.Weapon.OffHand", "Off hand weapon slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Ranged, "Equipment.Slot.Weapon.Ranged", "Ranged weapon slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Ranged_Bow, "Equipment.Slot.Weapon.Ranged.Bow", "Bow weapon slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Ranged_Crossbow, "Equipment.Slot.Weapon.Ranged.Crossbow", "Crossbow weapon slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Ranged_Rifle, "Equipment.Slot.Weapon.Ranged.Rifle", "Rifle weapon slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Ranged_Pistol, "Equipment.Slot.Weapon.Ranged.Pistol", "Pistol weapon slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Ranged_Shotgun, "Equipment.Slot.Weapon.Ranged.Shotgun", "Shotgun weapon slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Ranged_MachineGun, "Equipment.Slot.Weapon.Ranged.MachineGun", "Machine gun weapon slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Ranged_Sniper, "Equipment.Slot.Weapon.Ranged.Sniper", "Sniper weapon slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Ranged_Cannon, "Equipment.Slot.Weapon.Ranged.Cannon", "Cannon weapon slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Ranged_Launcher, "Equipment.Slot.Weapon.Ranged.Launcher", "Launcher weapon slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Melee, "Equipment.Slot.Weapon.Melee", "Melee weapon slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Melee_OneHanded, "Equipment.Slot.Weapon.Melee.OneHanded", "One-handed melee weapon slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Melee_TwoHanded, "Equipment.Slot.Weapon.Melee.TwoHanded", "Two-handed melee weapon slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Melee_Thrown, "Equipment.Slot.Weapon.Melee.Thrown", "Thrown melee weapon slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Melee_Staff, "Equipment.Slot.Weapon.Melee.Staff", "Staff weapon slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Melee_Shield, "Equipment.Slot.Weapon.Melee.Shield", "Shield slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Melee_Gauntlet, "Equipment.Slot.Weapon.Melee.Gauntlet", "Gauntlet weapon slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Melee_Polearm, "Equipment.Slot.Weapon.Melee.Polearm", "Polearm weapon slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Melee_Dagger, "Equipment.Slot.Weapon.Melee.Dagger", "Dagger weapon slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Melee_Sword, "Equipment.Slot.Weapon.Melee.Sword", "Sword weapon slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Melee_Axe, "Equipment.Slot.Weapon.Melee.Axe", "Axe weapon slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Melee_Mace, "Equipment.Slot.Weapon.Melee.Mace", "Mace weapon slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Melee_Spear, "Equipment.Slot.Weapon.Melee.Spear", "Spear weapon slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Melee_Fist, "Equipment.Slot.Weapon.Melee.Fist", "Fist weapon slot");

		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Magic, "Equipment.Slot.Weapon.Magic", "Magic weapon slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Magic_Staff, "Equipment.Slot.Weapon.Magic.Staff", "Magic staff slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Magic_Wand, "Equipment.Slot.Weapon.Magic.Wand", "Magic wand slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Magic_Orb, "Equipment.Slot.Weapon.Magic.Orb", "Magic orb slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Magic_Tome, "Equipment.Slot.Weapon.Magic.Tome", "Magic tome slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Magic_Rune, "Equipment.Slot.Weapon.Magic.Rune", "Magic rune slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Magic_Amulet, "Equipment.Slot.Weapon.Magic.Amulet", "Magic amulet slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Magic_Relic, "Equipment.Slot.Weapon.Magic.Relic", "Magic relic slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Magic_Special, "Equipment.Slot.Weapon.Magic.Special", "Special magic weapon slot");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Equipment_Slot_Weapon_Magic_Hand, "Equipment.Slot.Weapon.Magic.Hand", "Magic hand weapon slot");
	}

	namespace MultiplayerTags
	{
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Team, "Team", "Base tag for all teams");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Team_All, "Team.All", "All teams");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Team_A, "Team.A", "Team A");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Team_B, "Team.B", "Team B");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Team_C, "Team.C", "Team C");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Team_D, "Team.D", "Team D");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Role, "Role", "Base tag for all roles");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Role_None, "Role.None", "No role");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Role_Tank, "Role.Tank", "Tank role");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Role_DPS, "Role.DPS", "Damage-per-second role");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Role_Support, "Role.Support", "Support role");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Role_Flex, "Role.Flex", "Flex role");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Role_Healer, "Role.Healer", "Healer role");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Role_Scout, "Role.Scout", "Scout role");
	}
	
	namespace ObjectTags
	{
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Object, "Object", "Base tag for all interactive objects");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Object_Traversable, "Object.Traversable", "Objects that can be climbed or traversed");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Object_Grabbable, "Object.Grabbable", "Objects that can be grabbed and carried");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Object_Movable, "Object.Movable", "Objects that can be moved or pushed");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Object_Interactable, "Object.Interactable", "Objects that can be interacted with");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Object_Destructible, "Object.Destructible", "Objects that can be destroyed");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Object_Pickupable, "Object.Pickupable", "Objects that can be picked up");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Object_Physics, "Object.Physics", "Objects that are physics-based");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Object_Pickupable_Weapon, "Object.Pickupable.Weapon", "Pickupable objects that are weapons");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Object_Pickupable_Item, "Object.Pickupable.Item", "Pickupable objects that are items");
	}

	namespace PerkTags
	{
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Perk, "Perk", "Base tag for all perks");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Perk_Aggro, "Perk.Aggro", "Base tag for all aggro perks");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Perk_Aggro_Increase, "Perk.Aggro.Increase", "Perk that increases aggro");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Perk_Aggro_Decrease, "Perk.Aggro.Decrease", "Perk that decreases aggro");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Perk_Aggro_Ignore, "Perk.Aggro.Ignore", "Perk that ignores aggro");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Perk_Aggro_Ignore_Team, "Perk.Aggro.Ignore.Team", "Perk that ignores aggro from team members");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Perk_Aggro_Ignore_Friendly, "Perk.Aggro.Ignore.Friendly", "Perk that ignores aggro from friendly targets");
		UE_DEFINE_GAMEPLAY_TAG_COMMENT(Perk_Aggro_Ignore_Enemy, "Perk.Aggro.Ignore.Enemy", "Perk that ignores aggro from enemy targets");
	}
}
