// Fill out your copyright notice in the Description page of Project Settings.

#include "UI/ViewModels/UserAgreementViewModel.h"
#include "Kismet/KismetSystemLibrary.h" // For QuitGame

   void UUserAgreementViewModel::SetAgreementText(const FText& NewText)
   {
   	if (!AgreementText.IdenticalTo(NewText))
   	{
   		AgreementText = NewText;
   		UE_MVVM_BROADCAST_FIELD_VALUE_CHANGED(AgreementText);
   	}
   	// TODO: Load actual agreement text here, e.g., from FPlatformMisc::LoadTextResource
   	// For now, using placeholder:
   	if (AgreementText.IsEmpty())
   	{
   		SetAgreementText(FText::FromString(TEXT("Placeholder End User License Agreement Text...\n\nScroll down to read more...\n\nDo you accept?")));
   	}
   }

void UUserAgreementViewModel::AcceptAgreement()
   {
   	UE_LOG(LogTemp, Log, TEXT("User Agreement Accepted."));
   	OnAgreementDecisionMade.Broadcast(true); // Notify manager/subsystem
   }

void UUserAgreementViewModel::DeclineAgreement()
   {
   	UE_LOG(LogTemp, Log, TEXT("User Agreement Declined. Quitting game."));
   	OnAgreementDecisionMade.Broadcast(false); // Notify manager/subsystem

   	// Typically, declining the EULA means quitting the game
   	APlayerController* PC = GetWorld() ? GetWorld()->GetFirstPlayerController() : nullptr;
   	UKismetSystemLibrary::QuitGame(GetWorld(), PC, EQuitPreference::Quit, true);
   }
    