#include "Input/Handlers/Cal_InputHandler_CharacterSprint.h"
#include "InputAction.h"
#include "NinjaInputTags.h"
#include "Characters/CalamityCharacter.h"
#include "Components/NinjaInputManagerComponent.h"
#include "Types/FCharacterInputState.h"
#include "GameFramework/Character.h"
#include "GameFramework/CharacterMovementComponent.h"

UCal_InputHandler_CharacterSprint::UCal_InputHandler_CharacterSprint()
{
    bToggle = true;
    MinimumMagnitudeToSprint = 0.1f;
    
    BlockSprintTags = FGameplayTagContainer::EmptyContainer;
    BlockSprintTags.AddTagFast(Tag_Input_Block_Movement);
    
    TriggerEvents.Add(ETriggerEvent::Triggered);
    // Always add Completed event to handle hold mode
    TriggerEvents.Add(ETriggerEvent::Completed);

    static ConstructorHelpers::FObjectFinder<UInputAction> InputActionRef(TEXT("/Script/EnhancedInput.InputAction'/Calamity/Input/Actions/IA_Sprint.IA_Sprint'"));
    if (InputActionRef.Succeeded())
    {
        const TObjectPtr<UInputAction> InputAction = InputActionRef.Object;
        InputActions.AddUnique(InputAction);
    }
}

bool UCal_InputHandler_CharacterSprint::CanSprint_Implementation(UNinjaInputManagerComponent* Manager, 
    const FInputActionValue& Value) const
{
    // Only check if we have blocking tags, don't check WantsToSprint here
    // as that would prevent toggling from false to true
    return !HasAnyTags(Manager, BlockSprintTags);
}

bool UCal_InputHandler_CharacterSprint::ShouldStopSprinting_Implementation(UNinjaInputManagerComponent* Manager,
    const FInputActionValue& Value) const
{
    return HasAnyTags(Manager, BlockSprintTags);
}

void UCal_InputHandler_CharacterSprint::HandleTriggeredEvent_Implementation(UNinjaInputManagerComponent* Manager,
    const FInputActionValue& Value, const UInputAction* InputAction, float ElapsedTime) const
{
    if (!IsValid(Manager))
    {
        return;
    }

    if (ACalamityCharacterBase* Character = Cast<ACalamityCharacterBase>(Manager->GetPawn()))
    {
        if (bToggle)
        {
            // Toggle mode: flip the sprint state when triggered
            if (CanSprint(Manager, Value))
            {
                Character->SprintInput(!Character->ChrInputState.WantsToSprint);
            }
        }
        else
        {
            // Hold mode: start sprinting when triggered
            if (CanSprint(Manager, Value))
            {
                Character->SprintInput(true);
            }
        }
    }
}

void UCal_InputHandler_CharacterSprint::HandleCompletedEvent_Implementation(UNinjaInputManagerComponent* Manager,
    const FInputActionValue& Value, const UInputAction* InputAction) const
{
    Super::HandleCompletedEvent_Implementation(Manager, Value, InputAction);
    if (!IsValid(Manager))
    {
        return;
    }

    // Only handle completed event in hold mode
    if (!bToggle)
    {
        if (ACalamityCharacterBase* Character = Cast<ACalamityCharacterBase>(Manager->GetPawn()))
        {
            Character->SprintInput(false);
        }
    }
}