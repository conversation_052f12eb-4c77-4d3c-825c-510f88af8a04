#include "Input/Handlers/Cal_InputHandler_CharacterJump.h"
#include "InputAction.h"
#include "NinjaInputTags.h"
#include "Characters/CalamityCharacter.h"
#include "Components/NinjaInputManagerComponent.h"

UCal_InputHandler_CharacterJump::UCal_InputHandler_CharacterJump()
{
    MinimumMagnitudeToJump = 0.1f;
    
    BlockJumpTags = FGameplayTagContainer::EmptyContainer;
    BlockJumpTags.AddTagFast(Tag_Input_Block_Movement);
    
    TriggerEvents.Add(ETriggerEvent::Started);
    TriggerEvents.Add(ETriggerEvent::Triggered);
    TriggerEvents.Add(ETriggerEvent::Completed);

    static ConstructorHelpers::FObjectFinder<UInputAction> InputActionRef(TEXT("/Script/EnhancedInput.InputAction'/Calamity/Input/Actions/IA_Jump.IA_Jump'"));
    if (InputActionRef.Succeeded())
    {
        const TObjectPtr<UInputAction> InputAction = InputActionRef.Object;
        InputActions.AddUnique(InputAction);
    }
}

bool UCal_InputHandler_CharacterJump::CanJump_Implementation(UNinjaInputManagerComponent* Manager, 
    const FInputActionValue& Value) const
{
    return !HasAnyTags(Manager, BlockJumpTags) && Value.GetMagnitude() >= MinimumMagnitudeToJump;
}

void UCal_InputHandler_CharacterJump::HandleTriggeredEvent_Implementation(UNinjaInputManagerComponent* Manager,
    const FInputActionValue& Value, const UInputAction* InputAction, float ElapsedTime) const
{
    if (!CanJump(Manager, Value))
    {
        return;
    }

    if (ACalamityCharacter* Character = Cast<ACalamityCharacter>(Manager->GetPawn()))
    {
        Character->Jump();
    }
}

void UCal_InputHandler_CharacterJump::HandleCompletedEvent_Implementation(UNinjaInputManagerComponent* Manager,
    const FInputActionValue& Value, const UInputAction* InputAction) const
{
    Super::HandleCompletedEvent_Implementation(Manager, Value, InputAction);
}
