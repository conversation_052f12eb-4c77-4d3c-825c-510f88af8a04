#include "InventorySystem/Items/Weapons/CalamityMeleeWeapon.h"
#include "Characters/CalamityCharacter.h"
#include "AbilitySystem/Components/CalamityAbilitySystemComponent.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetSystemLibrary.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Components/SkeletalMeshComponent.h"
#include "Net/UnrealNetwork.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "Characters/CalamityCharacterBase.h"
#include "Engine/DamageEvents.h"
#include "GameFramework/Controller.h"
#include "PhysicalMaterials/PhysicalMaterial.h"

ACalamityMeleeWeapon::ACalamityMeleeWeapon()
{
    // Set this actor to call Tick() every frame
    PrimaryActorTick.bCanEverTick = true;
    
    // Setup defaults for melee weapon
    bUseSwingTraces = true;
    WeaponTraceRadius = 5.0f;
    MaxChargeTime = 2.0f;
    ComboResetTime = 2.0f;
    CurrentComboCount = 0;
    bIsAttacking = false;
    bIsChargingAttack = false;
    bIsBlocking = false;
    
    // Create trail component
    TrailComponent = CreateDefaultSubobject<UNiagaraComponent>(TEXT("TrailComponent"));
    TrailComponent->SetupAttachment(GetRootComponent());
    TrailComponent->SetAutoActivate(false);
}

void ACalamityMeleeWeapon::BeginPlay()
{
    Super::BeginPlay();
    
    // Setup trail if available
    if (TrailEffect)
    {
        TrailComponent->SetAsset(TrailEffect);
    }
}

void ACalamityMeleeWeapon::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    // Update charge time if charging attack
    if (bIsChargingAttack)
    {
        CurrentChargeTime = GetWorld()->GetTimeSeconds() - ChargeStartTime;
        if (CurrentChargeTime >= MaxChargeTime)
        {
            // Auto-release when fully charged
            ReleaseChargedAttack();
        }
    }
    
    // Perform weapon traces if attacking
    if (bIsAttacking && bUseSwingTraces)
    {
        PerformWeaponTrace();
    }
}

//---------- Attack Methods ----------//

void ACalamityMeleeWeapon::StartLightAttack()
{
    if (bIsAttacking || bIsChargingAttack || bIsBlocking)
    {
        return;
    }
    
    // Reset combo if it's been too long since last attack
    float CurrentTime = GetWorld()->GetTimeSeconds();
    if (CurrentTime - LastAttackTime > ComboResetTime)
    {
        ResetCombo();
    }
    
    bIsAttacking = true;
    
    // Select montage based on combo count
    if (LightAttackMontages.IsValidIndex(CurrentComboCount))
    {
        UAnimMontage* Montage = LightAttackMontages[CurrentComboCount];
        if (OwnerCharacter && Montage)
        {
            // Play attack animation
            OwnerCharacter->PlayAnimMontage(Montage);
            
            // Play swing effects
            PlaySwingEffects();
        }
    }
    
    // Update combo count
    CurrentComboCount = (CurrentComboCount + 1) % LightAttackMontages.Num();
    if (CurrentComboCount == 0)
    {
        CurrentComboCount = LightAttackMontages.Num() > 0 ? 1 : 0;
    }
    
    // Call blueprint event
    OnComboUpdated(CurrentComboCount);
    
    // Update last attack time
    LastAttackTime = CurrentTime;
    
    // Clear hit actors for new attack
    ClearHitActors();
}

void ACalamityMeleeWeapon::StartHeavyAttack()
{
    if (bIsAttacking || bIsChargingAttack || bIsBlocking)
    {
        return;
    }
    
    bIsAttacking = true;
    
    // Select montage based on combo count
    if (HeavyAttackMontages.IsValidIndex(CurrentComboCount))
    {
        UAnimMontage* Montage = HeavyAttackMontages[CurrentComboCount];
        if (OwnerCharacter && Montage)
        {
            // Play attack animation
            OwnerCharacter->PlayAnimMontage(Montage);
            
            // Play swing effects
            PlaySwingEffects();
        }
    }
    
    // Reset combo after heavy attack
    ResetCombo();
    
    // Update last attack time
    LastAttackTime = GetWorld()->GetTimeSeconds();
    
    // Clear hit actors for new attack
    ClearHitActors();
}

void ACalamityMeleeWeapon::StartChargedAttack()
{
    if (bIsAttacking || bIsChargingAttack || bIsBlocking)
    {
        return;
    }
    
    bIsChargingAttack = true;
    ChargeStartTime = GetWorld()->GetTimeSeconds();
    CurrentChargeTime = 0.0f;
}

void ACalamityMeleeWeapon::ReleaseChargedAttack()
{
    if (!bIsChargingAttack)
    {
        return;
    }
    
    bIsChargingAttack = false;
    bIsAttacking = true;
    
    // Play charged attack montage
    if (OwnerCharacter && ChargedAttackMontage)
    {
        // Play attack animation
        OwnerCharacter->PlayAnimMontage(ChargedAttackMontage);
        
        // Play swing effects
        PlaySwingEffects();
    }
    
    // Reset combo after charged attack
    ResetCombo();
    
    // Update last attack time
    LastAttackTime = GetWorld()->GetTimeSeconds();
    
    // Clear hit actors for new attack
    ClearHitActors();
}

void ACalamityMeleeWeapon::StartBlock()
{
    if (bIsAttacking || bIsChargingAttack || bIsBlocking)
    {
        return;
    }
    
    bIsBlocking = true;
    
    // Call blueprint event
    OnBlockStart();
    
    // Play block effects
    PlayBlockEffects();
}

void ACalamityMeleeWeapon::StopBlock()
{
    if (!bIsBlocking)
    {
        return;
    }
    
    bIsBlocking = false;
    
    // Call blueprint event
    OnBlockEnd();
}

void ACalamityMeleeWeapon::Parry()
{
    if (bIsAttacking || bIsChargingAttack)
    {
        return;
    }
    
    // Play parry animation
    if (OwnerCharacter && ParryMontage)
    {
        OwnerCharacter->PlayAnimMontage(ParryMontage);
    }
    
    // Play parry effects
    PlayParryEffects();
}

void ACalamityMeleeWeapon::PerformCombo()
{
    // This can be called from animation notifies to chain attacks
    // The weapon is already in attacking state at this point
}

void ACalamityMeleeWeapon::ResetCombo()
{
    CurrentComboCount = 0;
    GetWorldTimerManager().ClearTimer(ComboResetTimerHandle);
}

//---------- Damage Calculation ----------//

float ACalamityMeleeWeapon::CalculateLightAttackDamage() const
{
    // Base damage calculation for light attacks
    float BaseDamage = 15.0f;
    float DamageMultiplier = 1.0f;
    
    // Apply power state multipliers
    if (CurrentPowerState == EPowerState::Rift)
    {
        DamageMultiplier = 1.5f;
    }
    else if (CurrentPowerState == EPowerState::Radiance)
    {
        DamageMultiplier = 1.25f;
    }
    
    // Apply combo multiplier
    float ComboMultiplier = 1.0f + (0.1f * CurrentComboCount);
    
    return BaseDamage * DamageMultiplier * ComboMultiplier;
}

float ACalamityMeleeWeapon::CalculateHeavyAttackDamage() const
{
    // Base damage calculation for heavy attacks
    float BaseDamage = 30.0f;
    float DamageMultiplier = 1.0f;
    
    // Apply power state multipliers
    if (CurrentPowerState == EPowerState::Rift)
    {
        DamageMultiplier = 1.75f;
    }
    else if (CurrentPowerState == EPowerState::Radiance)
    {
        DamageMultiplier = 1.5f;
    }
    
    return BaseDamage * DamageMultiplier;
}

float ACalamityMeleeWeapon::CalculateChargedAttackDamage() const
{
    // Base damage calculation for charged attacks
    float BaseDamage = 50.0f;
    float DamageMultiplier = 1.0f;
    
    // Apply power state multipliers
    if (CurrentPowerState == EPowerState::Rift)
    {
        DamageMultiplier = 2.0f;
    }
    else if (CurrentPowerState == EPowerState::Radiance)
    {
        DamageMultiplier = 1.75f;
    }
    
    // Apply charge time multiplier (1.0 to 2.0 based on charge percentage)
    float ChargePercentage = FMath::Clamp(CurrentChargeTime / MaxChargeTime, 0.0f, 1.0f);
    float ChargeMultiplier = 1.0f + ChargePercentage;
    
    return BaseDamage * DamageMultiplier * ChargeMultiplier;
}

//---------- Weapon Traces ----------//

void ACalamityMeleeWeapon::PerformWeaponTrace()
{
    if (!OwnerCharacter || WeaponTraceSocketNames.Num() == 0)
    {
        return;
    }
    
    USkeletalMeshComponent* OwnerMesh = OwnerCharacter->GetMesh();
    if (!OwnerMesh)
    {
        return;
    }
    
    // Weapon trace parameters
    FCollisionQueryParams QueryParams;
    QueryParams.AddIgnoredActor(this);
    QueryParams.AddIgnoredActor(OwnerCharacter);
    QueryParams.bReturnPhysicalMaterial = true;
    
    TArray<FHitResult> HitResults;
    
    // Perform traces from socket locations
    for (const FName& SocketName : WeaponTraceSocketNames)
    {
        if (!OwnerMesh->DoesSocketExist(SocketName))
        {
            continue;
        }
        
        // Get socket transform
        FTransform SocketTransform = OwnerMesh->GetSocketTransform(SocketName);
        FVector TraceStart = SocketTransform.GetLocation();
        
        // Perform sphere trace
        TArray<FHitResult> TraceHits;
        bool bHit = GetWorld()->SweepMultiByChannel(
            TraceHits,
            TraceStart,
            TraceStart, // Same start and end for a sphere trace at point
            FQuat::Identity,
            ECC_Pawn,
            FCollisionShape::MakeSphere(WeaponTraceRadius),
            QueryParams
        );
        
        if (bHit)
        {
            for (FHitResult& Hit : TraceHits)
            {
                // Skip already hit actors
                if (AlreadyHitActors.Contains(Hit.GetActor()))
                {
                    continue;
                }
                
                // Skip null, self and owner
                if (!Hit.GetActor() || Hit.GetActor() == this || Hit.GetActor() == OwnerCharacter)
                {
                    continue;
                }
                
                // Add to already hit actors to prevent multiple hits in same attack
                AlreadyHitActors.Add(Hit.GetActor());
                
                // Apply damage based on attack type
                float DamageAmount = CalculateLightAttackDamage();
                if (HeavyAttackMontages.Contains(OwnerCharacter->GetCurrentMontage()))
                {
                    DamageAmount = CalculateHeavyAttackDamage();
                }
                else if (OwnerCharacter->GetCurrentMontage() == ChargedAttackMontage)
                {
                    DamageAmount = CalculateChargedAttackDamage();
                }
                
                // Apply damage
                FDamageEvent DamageEvent;
                Hit.GetActor()->TakeDamage(DamageAmount, DamageEvent, OwnerCharacter->GetController(), this);
                
                // Play impact effects
                PlayImpactEffects(Hit);
                
                // Notify blueprint
                OnAttackHit(Hit, DamageAmount);
                
                // Add to hit results
                HitResults.Add(Hit);
            }
        }
    }
    
    // Store hit results
    CurrentHitResults = HitResults;
}

void ACalamityMeleeWeapon::ClearHitActors()
{
    AlreadyHitActors.Empty();
    CurrentHitResults.Empty();
}

//---------- Effects ----------//

void ACalamityMeleeWeapon::ActivateWeaponTrail(bool bActivate)
{
    if (TrailComponent)
    {
        if (bActivate)
        {
            TrailComponent->Activate();
        }
        else
        {
            TrailComponent->Deactivate();
        }
    }
}

void ACalamityMeleeWeapon::PlaySwingEffects()
{
    // Play swing VFX
    if (SwingEffect)
    {
        UNiagaraFunctionLibrary::SpawnSystemAttached(
            SwingEffect,
            GetRootComponent(),
            NAME_None,
            FVector::ZeroVector,
            FRotator::ZeroRotator,
            EAttachLocation::SnapToTarget,
            true
        );
    }
    
    // Play swing sound
    if (SwingSound)
    {
        UGameplayStatics::PlaySoundAtLocation(this, SwingSound, GetActorLocation());
    }
    
    // Activate weapon trail
    ActivateWeaponTrail(true);
    
    // Call multicast to play effects on all clients
    Multicast_PlaySwingEffects();
}

void ACalamityMeleeWeapon::PlayImpactEffects(const FHitResult& Hit)
{
    // Play impact VFX
    if (ImpactEffect)
    {
        UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            ImpactEffect,
            Hit.ImpactPoint,
            Hit.ImpactNormal.Rotation()
        );
    }
    
    // Play impact sound
    if (ImpactSound)
    {
        UGameplayStatics::PlaySoundAtLocation(this, ImpactSound, Hit.ImpactPoint);
    }
}

void ACalamityMeleeWeapon::PlayBlockEffects()
{
    // Play block VFX and sounds
    // Implementation would be similar to swing effects
    
    // Call multicast to play effects on all clients
    Multicast_PlayBlockEffects();
}

void ACalamityMeleeWeapon::PlayParryEffects()
{
    // Play parry VFX and sounds
    // Implementation would be similar to swing effects
    
    // Call multicast to play effects on all clients
    Multicast_PlayParryEffects();
}

//---------- Network RPC Implementations ----------//

void ACalamityMeleeWeapon::Server_StartLightAttack_Implementation()
{
    StartLightAttack();
}

bool ACalamityMeleeWeapon::Server_StartLightAttack_Validate()
{
    return true;
}

void ACalamityMeleeWeapon::Server_StartHeavyAttack_Implementation()
{
    StartHeavyAttack();
}

bool ACalamityMeleeWeapon::Server_StartHeavyAttack_Validate()
{
    return true;
}

void ACalamityMeleeWeapon::Server_StartChargedAttack_Implementation()
{
    StartChargedAttack();
}

bool ACalamityMeleeWeapon::Server_StartChargedAttack_Validate()
{
    return true;
}

void ACalamityMeleeWeapon::Server_ReleaseChargedAttack_Implementation()
{
    ReleaseChargedAttack();
}

bool ACalamityMeleeWeapon::Server_ReleaseChargedAttack_Validate()
{
    return true;
}

void ACalamityMeleeWeapon::Server_StartBlock_Implementation()
{
    StartBlock();
}

bool ACalamityMeleeWeapon::Server_StartBlock_Validate()
{
    return true;
}

void ACalamityMeleeWeapon::Server_StopBlock_Implementation()
{
    StopBlock();
}

bool ACalamityMeleeWeapon::Server_StopBlock_Validate()
{
    return true;
}

void ACalamityMeleeWeapon::Server_Parry_Implementation()
{
    Parry();
}

bool ACalamityMeleeWeapon::Server_Parry_Validate()
{
    return true;
}

void ACalamityMeleeWeapon::Multicast_PlaySwingEffects_Implementation()
{
    // Don't duplicate effects on server
    if (GetLocalRole() == ROLE_Authority)
    {
        return;
    }
    
    // Play swing effects locally on all clients
    PlaySwingEffects();
}

void ACalamityMeleeWeapon::Multicast_PlayBlockEffects_Implementation()
{
    // Don't duplicate effects on server
    if (GetLocalRole() == ROLE_Authority)
    {
        return;
    }
    
    // Play block effects locally on all clients
    PlayBlockEffects();
}

void ACalamityMeleeWeapon::Multicast_PlayParryEffects_Implementation()
{
    // Don't duplicate effects on server
    if (GetLocalRole() == ROLE_Authority)
    {
        return;
    }
    
    // Play parry effects locally on all clients
    PlayParryEffects();
}

//---------- Interface Implementations ----------//

void ACalamityMeleeWeapon::Multicast_PlayImpactEffects_Implementation(const FHitResult& Hit)
{
}

void ACalamityMeleeWeapon::StartPrimaryAttack_Implementation()
{
    // Primary attack is light attack for melee weapons
    if (GetLocalRole() < ROLE_Authority)
    {
        Server_StartLightAttack();
    }
    else
    {
        StartLightAttack();
    }
}

void ACalamityMeleeWeapon::StopPrimaryAttack_Implementation()
{
    // Nothing to do for melee weapons
}

void ACalamityMeleeWeapon::StartSecondaryAttack_Implementation()
{
    // Secondary attack is heavy attack for melee weapons
    if (GetLocalRole() < ROLE_Authority)
    {
        Server_StartHeavyAttack();
    }
    else
    {
        StartHeavyAttack();
    }
}

void ACalamityMeleeWeapon::StopSecondaryAttack_Implementation()
{
    // Nothing to do for melee weapons
}

void ACalamityMeleeWeapon::PerformSpecialAttack_Implementation()
{
    // Special attack is charged attack for melee weapons
    if (GetLocalRole() < ROLE_Authority)
    {
        Server_StartChargedAttack();
    }
    else
    {
        StartChargedAttack();
    }
}

//---------- Network Properties ----------//

void ACalamityMeleeWeapon::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);
    
    // Replicate attack states
    DOREPLIFETIME(ACalamityMeleeWeapon, bIsAttacking);
    DOREPLIFETIME(ACalamityMeleeWeapon, bIsChargingAttack);
    DOREPLIFETIME(ACalamityMeleeWeapon, bIsBlocking);
    DOREPLIFETIME(ACalamityMeleeWeapon, CurrentComboCount);
    DOREPLIFETIME(ACalamityMeleeWeapon, ChargeStartTime);
    DOREPLIFETIME(ACalamityMeleeWeapon, CurrentChargeTime);
}
