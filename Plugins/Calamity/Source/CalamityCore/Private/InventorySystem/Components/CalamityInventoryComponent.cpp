#include "InventorySystem/Components/CalamityInventoryComponent.h"
#include "Characters/CalamityCharacter.h"
#include "Interfaces/IItemInterface.h"
#include "InventorySystem/Components/CalamityEquipmentComponent.h"
#include "InventorySystem/Items/CalamityWorldItem.h"
#include "InventorySystem/Data/Fragments/CalamityWorldRepresentationFragment.h"
#include "InventorySystem/Data/Fragments/CalamityEquippableFragment.h"
#include "InventorySystem/Items/CalamityInventoryItem.h"
#include "Net/UnrealNetwork.h"

UCalamityInventoryComponent::UCalamityInventoryComponent()
{
    PrimaryComponentTick.bCanEverTick = false;
    
    InventoryCapacity = 30;
    MaxWeight = 100.0f;
    CurrentWeight = 0.0f;
}

void UCalamityInventoryComponent::BeginPlay()
{
    Super::BeginPlay();
    
    // Setup equipment slots
    SetupEquipmentSlots();
}

void UCalamityInventoryComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
}

void UCalamityInventoryComponent::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);
    
    DOREPLIFETIME(UCalamityInventoryComponent, InventorySlots);
    DOREPLIFETIME(UCalamityInventoryComponent, EquipmentSlots);
    DOREPLIFETIME(UCalamityInventoryComponent, InventoryCapacity);
    DOREPLIFETIME(UCalamityInventoryComponent, MaxWeight);
    DOREPLIFETIME(UCalamityInventoryComponent, CurrentWeight);
}

// Item management
UCalamityInventoryItem* UCalamityInventoryComponent::CreateItemInstance(UCalamityItemData* ItemData)
{
    if (!ItemData)
    {
        return nullptr;
    }
    
    UCalamityInventoryItem* NewInstance = NewObject<UCalamityInventoryItem>(this);
    NewInstance->InitializeFromItemData(ItemData);
    
    return NewInstance;
}

bool UCalamityInventoryComponent::AddItem(UCalamityInventoryItem* Item, int32& OutSlotIndex)
{
    if (!Item)
    {
        return false;
    }
    
    // Check if we can add the item's weight
    if (!CanAddItemWeight(Item))
    {
        return false;
    }
    
    // Find the first empty slot
    int32 SlotIndex = FindFirstEmptySlot();
    if (SlotIndex == -1)
    {
        // No empty slots
        return false;
    }
    
    // Add the item to the slot
    FInventorySlot NewSlot;
    NewSlot.Item = Item;
    NewSlot.Quantity = 1;
    NewSlot.SlotIndex = SlotIndex;
    
    // Add to array
    if (InventorySlots.Num() <= SlotIndex)
    {
        InventorySlots.SetNum(SlotIndex + 1);
    }
    InventorySlots[SlotIndex] = NewSlot;
    
    // Update weight
    RecalculateWeight();
    
    // Set out param
    OutSlotIndex = SlotIndex;
    
    // Call RPC if we're the server
    if (GetOwner()->HasAuthority())
    {
        // No need to call Server_AddItem if we're already on the server
        //OnItemAdded.Broadcast(this, Item);
        OnInventoryChanged.Broadcast(this);
    }
    else
    {
        Server_AddItem(Item);
    }
    
    return true;
}

bool UCalamityInventoryComponent::AddItemByData(UCalamityItemData* ItemData, int32& OutSlotIndex)
{
    if (!ItemData)
    {
        return false;
    }
    
    UCalamityInventoryItem* NewItem = CreateItemInstance(ItemData);
    if (!NewItem)
    {
        return false;
    }
    
    return AddItem(NewItem, OutSlotIndex);
}

bool UCalamityInventoryComponent::RemoveItem(UCalamityInventoryItem* Item)
{
    if (!Item)
    {
        return false;
    }
    
    // Find the slot containing the item
    for (int32 i = 0; i < InventorySlots.Num(); ++i)
    {
        if (InventorySlots[i].Item == Item)
        {
            // Found it
            InventorySlots[i].Item = nullptr;
            InventorySlots[i].Quantity = 0;
            
            // Update weight
            RecalculateWeight();
            
            // Call RPC if we're the server
            if (GetOwner()->HasAuthority())
            {
                // No need to call Server_RemoveItem if we're already on the server
                //OnItemRemoved.Broadcast(this, Item);
                OnInventoryChanged.Broadcast(this);
            }
            else
            {
                Server_RemoveItem(Item);
            }
            
            return true;
        }
    }
    
    // Item not found
    return false;
}

bool UCalamityInventoryComponent::RemoveItemFromSlot(int32 SlotIndex)
{
    if (SlotIndex < 0 || SlotIndex >= InventorySlots.Num())
    {
        return false;
    }
    
    UCalamityInventoryItem* Item = InventorySlots[SlotIndex].Item;
    if (!Item)
    {
        return false;
    }
    
    // Remove the item
    InventorySlots[SlotIndex].Item = nullptr;
    InventorySlots[SlotIndex].Quantity = 0;
    
    // Update weight
    RecalculateWeight();
    
    // Call RPC if we're the server
    if (GetOwner()->HasAuthority())
    {
        // No need to call Server_RemoveItem if we're already on the server
        //OnItemRemoved.Broadcast(this, Item);
        OnInventoryChanged.Broadcast(this);
    }
    else
    {
        Server_RemoveItem(Item);
    }
    
    return true;
}

bool UCalamityInventoryComponent::UseItem(UCalamityInventoryItem* Item)
{
    if (!Item)
    {
        return false;
    }
    
    // Call RPC if we're the server
    if (GetOwner()->HasAuthority())
    {
        // Implement item usage logic
        // For now, just return true
        return true;
    }
    else
    {
        Server_UseItem(Item);
        return true;
    }
}

bool UCalamityInventoryComponent::UseItemFromSlot(int32 SlotIndex)
{
    if (SlotIndex < 0 || SlotIndex >= InventorySlots.Num())
    {
        return false;
    }
    
    UCalamityInventoryItem* Item = InventorySlots[SlotIndex].Item;
    if (!Item)
    {
        return false;
    }
    
    return UseItem(Item);
}

bool UCalamityInventoryComponent::DropItem(UCalamityInventoryItem* Item, int32 Count)
{
    if (!Item)
    {
        return false;
    }
    
    // Call RPC if we're the server
    if (GetOwner()->HasAuthority())
    {
        // Implement item dropping logic
        // For now, just remove the item
        RemoveItem(Item);
        return true;
    }
    else
    {
        Server_DropItem(Item, Count);
        return true;
    }
}

bool UCalamityInventoryComponent::DropItemFromSlot(int32 SlotIndex, int32 Count)
{
    if (SlotIndex < 0 || SlotIndex >= InventorySlots.Num())
    {
        return false;
    }
    
    UCalamityInventoryItem* Item = InventorySlots[SlotIndex].Item;
    if (!Item)
    {
        return false;
    }
    
    return DropItem(Item, Count);
}

UCalamityInventoryItem* UCalamityInventoryComponent::FindItemByData(UCalamityItemData* ItemData) const
{
    if (!ItemData)
    {
        return nullptr;
    }
    
    for (const FInventorySlot& Slot : InventorySlots)
    {
        if (Slot.Item && Slot.Item->CalamityItemData == ItemData)
        {
            return Slot.Item;
        }
    }
    
    return nullptr;
}

UCalamityInventoryItem* UCalamityInventoryComponent::FindItemByTag(const FGameplayTag& Tag) const
{
    if (!Tag.IsValid())
    {
        return nullptr;
    }
    
    for (const FInventorySlot& Slot : InventorySlots)
    {
        if (Slot.Item)
        {
            FGameplayTagContainer Tags;
            Slot.Item->GetAllTags(Tags);
            
            if (Tags.HasTag(Tag))
            {
                return Slot.Item;
            }
        }
    }
    
    return nullptr;
}

bool UCalamityInventoryComponent::SwitchToWeaponSlot(int32 WeaponSlotIndex)
{
    if (WeaponSlotIndex < 0 || WeaponSlotIndex > 3)
    {
        return false;
    }
    
    // Convert index to slot type
    EEquipmentSlot SlotType = EEquipmentSlot::None;
    
    switch (WeaponSlotIndex)
    {
        case 0: SlotType = EEquipmentSlot::Weapon1; break;
        case 1: SlotType = EEquipmentSlot::Weapon2; break;
        case 2: SlotType = EEquipmentSlot::Weapon3; break;
        case 3: SlotType = EEquipmentSlot::Weapon4; break;
    }
    
    // Check if the slot has a weapon
    UCalamityInventoryItem* Weapon = GetEquippedItem(SlotType);
    if (!Weapon)
    {
        return false;
    }
    
    // Call RPC if we're not the server
    if (!GetOwner()->HasAuthority())
    {
        Server_SwitchToWeaponSlot(WeaponSlotIndex);
    }
    
    // Switch to the weapon
    ACalamityCharacter* Character = GetCalamityCharacterOwner();
    if (Character)
    {
        UCalamityEquipmentComponent* EquipComp = Character->FindComponentByClass<UCalamityEquipmentComponent>();
        if (EquipComp)
        {
            EquipComp->SetActiveWeaponSlot(SlotType);
            return true;
        }
    }
    
    return false;
}

bool UCalamityInventoryComponent::NextWeapon()
{
    // Get the current active weapon slot
    ACalamityCharacter* Character = GetCalamityCharacterOwner();
    if (!Character)
    {
        return false;
    }
    
    UCalamityEquipmentComponent* EquipComp = Character->FindComponentByClass<UCalamityEquipmentComponent>();
    if (!EquipComp)
    {
        return false;
    }
    
    EEquipmentSlot CurrentSlot = EquipComp->GetActiveWeaponSlot();
    
    // Find the next weapon slot with a weapon
    int32 StartIndex = 0;
    
    switch (CurrentSlot)
    {
        case EEquipmentSlot::Weapon1: StartIndex = 1; break;
        case EEquipmentSlot::Weapon2: StartIndex = 2; break;
        case EEquipmentSlot::Weapon3: StartIndex = 3; break;
        case EEquipmentSlot::Weapon4: StartIndex = 0; break;
        default: StartIndex = 0; break;
    }
    
    // Try to find a weapon in the next slots
    for (int32 i = 0; i < 4; ++i)
    {
        int32 SlotIndex = (StartIndex + i) % 4;
        
        EEquipmentSlot SlotType = EEquipmentSlot::None;
        
        switch (SlotIndex)
        {
            case 0: SlotType = EEquipmentSlot::Weapon1; break;
            case 1: SlotType = EEquipmentSlot::Weapon2; break;
            case 2: SlotType = EEquipmentSlot::Weapon3; break;
            case 3: SlotType = EEquipmentSlot::Weapon4; break;
        }
        
        // Check if the slot has a weapon
        if (GetEquippedItem(SlotType))
        {
            return SwitchToWeaponSlot(SlotIndex);
        }
    }
    
    return false;
}

bool UCalamityInventoryComponent::PreviousWeapon()
{
    // Get the current active weapon slot
    ACalamityCharacter* Character = GetCalamityCharacterOwner();
    if (!Character)
    {
        return false;
    }
    
    UCalamityEquipmentComponent* EquipComp = Character->FindComponentByClass<UCalamityEquipmentComponent>();
    if (!EquipComp)
    {
        return false;
    }
    
    EEquipmentSlot CurrentSlot = EquipComp->GetActiveWeaponSlot();
    
    // Find the previous weapon slot with a weapon
    int32 StartIndex = 0;
    
    switch (CurrentSlot)
    {
        case EEquipmentSlot::Weapon1: StartIndex = 3; break;
        case EEquipmentSlot::Weapon2: StartIndex = 0; break;
        case EEquipmentSlot::Weapon3: StartIndex = 1; break;
        case EEquipmentSlot::Weapon4: StartIndex = 2; break;
        default: StartIndex = 3; break;
    }
    
    // Try to find a weapon in the previous slots
    for (int32 i = 0; i < 4; ++i)
    {
        int32 SlotIndex = (StartIndex - i + 4) % 4;
        
        EEquipmentSlot SlotType = EEquipmentSlot::None;
        
        switch (SlotIndex)
        {
            case 0: SlotType = EEquipmentSlot::Weapon1; break;
            case 1: SlotType = EEquipmentSlot::Weapon2; break;
            case 2: SlotType = EEquipmentSlot::Weapon3; break;
            case 3: SlotType = EEquipmentSlot::Weapon4; break;
        }
        
        // Check if the slot has a weapon
        if (GetEquippedItem(SlotType))
        {
            return SwitchToWeaponSlot(SlotIndex);
        }
    }
    
    return false;
}

UCalamityInventoryItem* UCalamityInventoryComponent::GetItemAtSlot(int32 SlotIndex) const
{
    if (SlotIndex < 0 || SlotIndex >= InventorySlots.Num())
    {
        return nullptr;
    }
    
    return InventorySlots[SlotIndex].Item;
}

UCalamityInventoryItem* UCalamityInventoryComponent::GetEquippedItem(EEquipmentSlot Slot) const
{
    for (const FEquipmentSlot& EquipSlot : EquipmentSlots)
    {
        if (EquipSlot.SlotType == Slot)
        {
            return EquipSlot.Item;
        }
    }
    
    return nullptr;
}

TArray<UCalamityInventoryItem*> UCalamityInventoryComponent::GetEquippedItemsByTag(const FGameplayTag& Tag) const
{
    TArray<UCalamityInventoryItem*> Result;
    
    for (const FEquipmentSlot& EquipSlot : EquipmentSlots)
    {
        if (EquipSlot.Item)
        {
            FGameplayTagContainer Tags;
            EquipSlot.Item->GetAllTags(Tags);
            
            if (Tags.HasTag(Tag))
            {
                Result.Add(EquipSlot.Item);
            }
        }
    }
    
    return Result;
}

bool UCalamityInventoryComponent::IsItemEquipped(UCalamityInventoryItem* Item) const
{
    if (!Item)
    {
        return false;
    }
    
    for (const FEquipmentSlot& EquipSlot : EquipmentSlots)
    {
        if (EquipSlot.Item == Item)
        {
            return true;
        }
    }
    
    return false;
}

bool UCalamityInventoryComponent::CanEquipItemToSlot(UCalamityInventoryItem* Item, EEquipmentSlot Slot) const
{
    if (!Item)
    {
        return false;
    }

    // First check if slot exists and is not already equipped
    UCalamityEquipmentComponent* EquipComp = GetOwner()->FindComponentByClass<UCalamityEquipmentComponent>();
    if (!EquipComp || EquipComp->IsSlotEquipped(Slot))
        return false;

    // Then check if item is compatible with this slot type
    UEquippableFragment* EquipFragment = Item->GetFragment<UEquippableFragment>();
    if (!EquipFragment)
        return false;

    // Check if the fragment can be used with this slot
    if (!EquipFragment->IsCompatibleWithSlot(Slot))
        return false;

    // Check if the slot matches the item's equipment slot or if the item can be equipped to any slot
    if (EquipFragment->EquipmentSlot != Slot && EquipFragment->EquipmentSlot != EEquipmentSlot::None)
    {
        return false;
    }

    // Check any other restrictions (level, class, etc.)
    if (const ACalamityCharacter* Character = GetCalamityCharacterOwner())
    {
        if (UCalamityEquipmentComponent* EquipmentComponent = Character->FindComponentByClass<UCalamityEquipmentComponent>())
        {
            return EquipFragment->CanEquip(EquipmentComponent);
        }
    }

    return true;
}

int32 UCalamityInventoryComponent::GetItemCount() const
{
    int32 Count = 0;
    
    for (const FInventorySlot& Slot : InventorySlots)
    {
        if (Slot.Item)
        {
            Count++;
        }
    }
    
    return Count;
}

bool UCalamityInventoryComponent::CanAddItem(UCalamityInventoryItem* ItemInstance)
{
    return ItemInstance && CanAddItemWeight(ItemInstance);
}

// Helper functions
int32 UCalamityInventoryComponent::FindFirstEmptySlot() const
{
    for (int32 i = 0; i < InventoryCapacity; ++i)
    {
        if (i >= InventorySlots.Num())
        {
            return i;
        }
        
        if (!InventorySlots[i].Item)
        {
            return i;
        }
    }
    
    return -1;
}

bool UCalamityInventoryComponent::CanAddItemWeight(UCalamityInventoryItem* Item) const
{
    // Calculate weight of the item (for now, just return true)
    // You would need to implement item weight calculation based on your game logic
    return true;
}

void UCalamityInventoryComponent::RecalculateWeight()
{
    CurrentWeight = 0.0f;
    
    for (const FInventorySlot& Slot : InventorySlots)
    {
        if (Slot.Item)
        {
            // Add the weight of the item (for now, just add 1.0f)
            // You would need to implement item weight calculation based on your game logic
            CurrentWeight += 1.0f;
        }
    }
}

void UCalamityInventoryComponent::SetupEquipmentSlots()
{
    // Create slots for all equipment types
    EquipmentSlots.Empty();
    
    // Add weapon slots
    FEquipmentSlot Weapon1Slot;
    Weapon1Slot.SlotType = EEquipmentSlot::Weapon1;
    EquipmentSlots.Add(Weapon1Slot);
    
    FEquipmentSlot Weapon2Slot;
    Weapon2Slot.SlotType = EEquipmentSlot::Weapon2;
    EquipmentSlots.Add(Weapon2Slot);
    
    FEquipmentSlot Weapon3Slot;
    Weapon3Slot.SlotType = EEquipmentSlot::Weapon3;
    EquipmentSlots.Add(Weapon3Slot);
    
    FEquipmentSlot Weapon4Slot;
    Weapon4Slot.SlotType = EEquipmentSlot::Weapon4;
    EquipmentSlots.Add(Weapon4Slot);
    
    // Add armor slots
    FEquipmentSlot HeadSlot;
    HeadSlot.SlotType = EEquipmentSlot::Head;
    EquipmentSlots.Add(HeadSlot);
    
    FEquipmentSlot ChestSlot;
    ChestSlot.SlotType = EEquipmentSlot::Chest;
    EquipmentSlots.Add(ChestSlot);
    
    FEquipmentSlot ArmsSlot;
    ArmsSlot.SlotType = EEquipmentSlot::Arms;
    EquipmentSlots.Add(ArmsSlot);
    
    FEquipmentSlot LegsSlot;
    LegsSlot.SlotType = EEquipmentSlot::Legs;
    EquipmentSlots.Add(LegsSlot);
    
    // Add accessory slots
    FEquipmentSlot Accessory1Slot;
    Accessory1Slot.SlotType = EEquipmentSlot::Accessory1;
    EquipmentSlots.Add(Accessory1Slot);
    
    FEquipmentSlot Accessory2Slot;
    Accessory2Slot.SlotType = EEquipmentSlot::Accessory2;
    EquipmentSlots.Add(Accessory2Slot);
}

ACalamityCharacter* UCalamityInventoryComponent::GetCalamityCharacterOwner() const
{
    return Cast<ACalamityCharacter>(GetOwner());
}

// RPC implementations
void UCalamityInventoryComponent::OnRep_InventorySlots()
{
    // Call BP event
    OnInventorySlotsChanged();
    
    // Broadcast inventory changed event
    OnInventoryChanged.Broadcast(this);
}

void UCalamityInventoryComponent::OnRep_EquipmentSlots()
{
    // Call BP event
    OnEquipmentSlotsChanged();
}

void UCalamityInventoryComponent::OnRep_CurrentWeight()
{
    // Call BP event
    OnWeightChanged();
}

bool UCalamityInventoryComponent::Server_AddItem_Validate(UCalamityInventoryItem* Item)
{
    return true;
}

void UCalamityInventoryComponent::Server_AddItem_Implementation(UCalamityInventoryItem* Item)
{
    int32 SlotIndex;
    AddItem(Item, SlotIndex);
}

bool UCalamityInventoryComponent::Server_RemoveItem_Validate(UCalamityInventoryItem* Item)
{
    return true;
}

void UCalamityInventoryComponent::Server_RemoveItem_Implementation(UCalamityInventoryItem* Item)
{
    RemoveItem(Item);
}

bool UCalamityInventoryComponent::Server_UseItem_Validate(UCalamityInventoryItem* Item)
{
    return true;
}

void UCalamityInventoryComponent::Server_UseItem_Implementation(UCalamityInventoryItem* Item)
{
    UseItem(Item);
}

bool UCalamityInventoryComponent::Server_DropItem_Validate(UCalamityInventoryItem* Item, int32 Count)
{
    return true;
}

void UCalamityInventoryComponent::Server_DropItem_Implementation(UCalamityInventoryItem* Item, int32 Count)
{
    DropItem(Item, Count);
}

bool UCalamityInventoryComponent::Server_EquipItem_Validate(UCalamityInventoryItem* Item, EEquipmentSlot Slot)
{
    return true;
}

void UCalamityInventoryComponent::Server_EquipItem_Implementation(UCalamityInventoryItem* Item, EEquipmentSlot Slot)
{
    EquipItem(Item, Slot);
}

bool UCalamityInventoryComponent::Server_UnequipItem_Validate(EEquipmentSlot Slot)
{
    return true;
}

void UCalamityInventoryComponent::Server_UnequipItem_Implementation(EEquipmentSlot Slot)
{
    UnequipItem(Slot);
}

bool UCalamityInventoryComponent::Server_SwitchToWeaponSlot_Validate(int32 WeaponSlotIndex)
{
    return true;
}

void UCalamityInventoryComponent::Server_SwitchToWeaponSlot_Implementation(int32 WeaponSlotIndex)
{
    SwitchToWeaponSlot(WeaponSlotIndex);
}

// Equipment Management
bool UCalamityInventoryComponent::EquipItem(UCalamityInventoryItem* Item, EEquipmentSlot Slot)
{
    if (!Item)
    {
        return false;
    }
    
    // Get the equippable fragment
    UEquippableFragment* EquipFragment = Item->GetFragment<UEquippableFragment>();
    if (!EquipFragment)
    {
        return false;
    }
    
    // If slot is None, use the item's preferred slot
    if (Slot == EEquipmentSlot::None)
    {
        Slot = EquipFragment->EquipmentSlot;
    }
    
    // Check if the item can be equipped to this slot
    if (!CanEquipItemToSlot(Item, Slot))
    {
        return false;
    }
    
    // Call RPC if we're not the server
    if (!GetOwner()->HasAuthority())
    {
        Server_EquipItem(Item, Slot);
        return true;
    }
    
    // Find the equipment slot
    for (FEquipmentSlot& EquipSlot : EquipmentSlots)
    {
        if (EquipSlot.SlotType == Slot)
        {
            // If there's already an item in the slot, unequip it
            if (EquipSlot.Item)
            {
                UnequipItem(Slot);
            }
            
            // Equip the new item
            EquipSlot.Item = Item;
            
            // Call the OnEquipped method
            ACalamityCharacter* Character = GetCalamityCharacterOwner();
            if (Character)
            {
                UCalamityEquipmentComponent* EquipComp = Character->FindComponentByClass<UCalamityEquipmentComponent>();
                if (EquipComp)
                {
                    EquipFragment->OnEquipped(EquipComp);
                }
            }
            
            // Broadcast the equipped event
            //OnEquippedItemChanged.Broadcast(Item);
            
            return true;
        }
    }
    
    return false;
}

bool UCalamityInventoryComponent::EquipItemFromSlot(int32 SlotIndex, EEquipmentSlot Slot)
{
    if (SlotIndex < 0 || SlotIndex >= InventorySlots.Num())
    {
        return false;
    }
    
    UCalamityInventoryItem* Item = InventorySlots[SlotIndex].Item;
    if (!Item)
    {
        return false;
    }
    
    return EquipItem(Item, Slot);
}

bool UCalamityInventoryComponent::UnequipItem(EEquipmentSlot Slot)
{
    // Find the equipment slot
    for (FEquipmentSlot& EquipSlot : EquipmentSlots)
    {
        if (EquipSlot.SlotType == Slot && EquipSlot.Item)
        {
            UCalamityInventoryItem* Item = EquipSlot.Item;
            
            // Get the equippable fragment
            UEquippableFragment* EquipFragment = Item->GetFragment<UEquippableFragment>();
            if (EquipFragment)
            {
                // Call the OnUnequipped method
                ACalamityCharacter* Character = GetCalamityCharacterOwner();
                if (Character)
                {
                    UCalamityEquipmentComponent* EquipComp = Character->FindComponentByClass<UCalamityEquipmentComponent>();
                    if (EquipComp)
                    {
                        EquipFragment->OnUnequipped(EquipComp);
                    }
                }
            }
            
            // Call RPC if we're not the server
            if (!GetOwner()->HasAuthority())
            {
                Server_UnequipItem(Slot);
                return true;
            }
            
            // Unequip the item
            EquipSlot.Item = nullptr;
            
            // Broadcast the unequipped event
            OnEquippedItemChanged.Broadcast(nullptr);
            
            return true;
        }
    }
    
    return false;
}

AActor* UCalamityInventoryComponent::SpawnWorldItem(UCalamityInventoryItem* Item, const FTransform& SpawnTransform, int32 Quantity)
{
    if (!Item || !GetOwner() || !GetOwner()->GetWorld())
    {
        return nullptr;
    }
    
    // Get the world item class to spawn
    TSubclassOf<AActor> WorldItemClass = GetWorldItemClassForItem(Item);
    if (!WorldItemClass)
    {
        // Default to base world item class if no specific class is found
        WorldItemClass = ACalamityWorldItem::StaticClass();
    }
    
    // Spawn the world item
    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
    
    AActor* SpawnedActor = GetOwner()->GetWorld()->SpawnActor<AActor>(WorldItemClass, SpawnTransform, SpawnParams);
    
    // Initialize the world item with the item instance
    if (SpawnedActor)
    {
        if (SpawnedActor->Implements<UIPickupInterface>())
        {
            // Set the item instance
            IItemInterface::Execute_InitializeFromInventoryItem(SpawnedActor, Item);
            
            // Set the quantity
            IIPickupInterface::Execute_SetQuantity(SpawnedActor, Quantity);
        }
    }
    
    return SpawnedActor;
}

AActor* UCalamityInventoryComponent::SpawnWorldItemFromData(UCalamityItemData* ItemData, const FTransform& SpawnTransform, int32 Quantity)
{
    if (!ItemData || !GetOwner() || !GetOwner()->GetWorld())
    {
        return nullptr;
    }
    
    // Create a temporary item instance
    UCalamityInventoryItem* TempItem = CreateItemInstance(ItemData);
    if (!TempItem)
    {
        return nullptr;
    }
    
    // Spawn the world item
    AActor* SpawnedActor = SpawnWorldItem(TempItem, SpawnTransform, Quantity);
    
    // If spawning failed, clean up the temporary item instance
    if (!SpawnedActor)
    {
        TempItem->MarkAsGarbage();
    }
    
    return SpawnedActor;
}

AActor* UCalamityInventoryComponent::DropItemInWorld(UCalamityInventoryItem* Item, int32 Count)
{
    if (!Item || Count <= 0 || !GetOwner())
    {
        return nullptr;
    }
    
    // Find the item in the inventory
    bool bFound = false;
    int32 SlotIndex = -1;
    
    for (int32 i = 0; i < InventorySlots.Num(); ++i)
    {
        if (InventorySlots[i].Item == Item)
        {
            SlotIndex = i;
            bFound = true;
            break;
        }
    }
    
    if (!bFound)
    {
        return nullptr;
    }
    
    // Calculate spawn transform
    FTransform SpawnTransform = GetOwner()->GetTransform();
    
    // Offset forward from the character
    ACalamityCharacter* Character = GetCalamityCharacterOwner();
    if (Character)
    {
        FVector ForwardVector = Character->GetActorForwardVector();
        SpawnTransform.AddToTranslation(ForwardVector * 100.0f);
    }
    
    // Spawn the world item
    AActor* SpawnedActor = SpawnWorldItem(Item, SpawnTransform, Count);
    
    // Remove the item from inventory if spawned successfully
    if (SpawnedActor)
    {
        // If we're dropping all of the item, remove it completely
        if (Count >= InventorySlots[SlotIndex].Quantity)
        {
            RemoveItemFromSlot(SlotIndex);
        }
        else
        {
            // Otherwise, reduce the quantity
            InventorySlots[SlotIndex].Quantity -= Count;
        }
    }
    
    return SpawnedActor;
}

AActor* UCalamityInventoryComponent::DropItemFromSlotInWorld(int32 SlotIndex, int32 Count)
{
    if (SlotIndex < 0 || SlotIndex >= InventorySlots.Num() || Count <= 0)
    {
        return nullptr;
    }
    
    UCalamityInventoryItem* Item = InventorySlots[SlotIndex].Item;
    if (!Item)
    {
        return nullptr;
    }
    
    return DropItemInWorld(Item, Count);
}

TSubclassOf<AActor> UCalamityInventoryComponent::GetWorldItemClassForItem(UCalamityInventoryItem* Item) const
{
    if (!Item)
    {
        return nullptr;
    }
    
    // Check if the item has a WorldRepresentationFragment
    UWorldRepresentationFragment* WorldRepFragment = Item->GetFragment<UWorldRepresentationFragment>();
    if (WorldRepFragment && WorldRepFragment->WorldActorClass)
    {
        return WorldRepFragment->WorldActorClass;
    }
    
    // Default to base world item class
    return ACalamityWorldItem::StaticClass();
}