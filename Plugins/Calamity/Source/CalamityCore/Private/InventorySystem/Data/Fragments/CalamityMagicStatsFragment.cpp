#include "InventorySystem/Data/Fragments/CalamityMagicStatsFragment.h"
#include "InventorySystem/Items/CalamityItemInstance.h"
#include "GameFramework/Actor.h"

UMagicStatsFragment::UMagicStatsFragment()
{
}

float UMagicStatsFragment::CalculateSpellDamage(const FSpellDefinition& Spell, bool bIsCritical, float ChargePercent) const
{
    // Base damage
    float Damage = Spell.BaseValue;
    
    // Apply magical power multiplier
    Damage *= (MagicalPower / 100.0f);
    
    // Apply elemental bonus if the spell's element matches one of the weapon's elements
    if (Spell.SpellElement == PrimaryElement || Spell.SpellElement == SecondaryElement)
    {
        Damage *= 1.2f; // 20% bonus for matching element
    }
    
    // Apply critical hit bonus
    if (bIsCritical)
    {
        Damage *= CriticalSpellMultiplier;
    }
    
    // Apply charge bonus
    if (Spell.bCanCharge && ChargePercent > 0.0f)
    {
        // Linear scaling with charge percent up to the charge multiplier
        float ChargeBonus = 1.0f + (Spell.ChargeMultiplier - 1.0f) * ChargePercent;
        Damage *= ChargeBonus;
    }
    
    // Apply tag bonus if the spell has any of the boosted tags
    if (DoesBoostedSpell(Spell.EffectTags))
    {
        Damage *= TaggedSpellBoostMultiplier;
    }
    
    return Damage;
}

float UMagicStatsFragment::CalculateSpellHealing(const FSpellDefinition& Spell, bool bIsCritical, float ChargePercent) const
{
    // Similar to damage calculation but for healing
    float Healing = Spell.BaseValue;
    
    // Apply magical power multiplier
    Healing *= (MagicalPower / 100.0f);
    
    // Apply elemental bonus if the spell's element matches one of the weapon's elements
    if (Spell.SpellElement == PrimaryElement || Spell.SpellElement == SecondaryElement)
    {
        Healing *= 1.2f; // 20% bonus for matching element
    }
    
    // Apply critical hit bonus
    if (bIsCritical)
    {
        Healing *= CriticalSpellMultiplier;
    }
    
    // Apply charge bonus
    if (Spell.bCanCharge && ChargePercent > 0.0f)
    {
        // Linear scaling with charge percent up to the charge multiplier
        float ChargeBonus = 1.0f + (Spell.ChargeMultiplier - 1.0f) * ChargePercent;
        Healing *= ChargeBonus;
    }
    
    // Apply tag bonus if the spell has any of the boosted tags
    if (DoesBoostedSpell(Spell.EffectTags))
    {
        Healing *= TaggedSpellBoostMultiplier;
    }
    
    return Healing;
}

float UMagicStatsFragment::GetAdjustedCastTime(const FSpellDefinition& Spell) const
{
    // Apply cast speed multiplier
    return Spell.CastTime / CastingSpeedMultiplier;
}

float UMagicStatsFragment::GetAdjustedCooldown(const FSpellDefinition& Spell) const
{
    // Apply cooldown reduction
    return Spell.Cooldown * (1.0f - CooldownReduction);
}

float UMagicStatsFragment::GetAdjustedEnergyCost(const FSpellDefinition& Spell, float ChannelTime) const
{
    // Base energy cost
    float Cost = Spell.EnergyCost;
    
    // Apply energy cost reduction
    Cost *= (1.0f - EnergyCostReduction);
    
    // Apply extended channeling cost increase
    if (Spell.bCanChannel && ChannelTime > Spell.MaxChannelTime && bCanChannelIndefinitely)
    {
        // Increase cost for extended channeling
        float OverChannelTime = ChannelTime - Spell.MaxChannelTime;
        float OverChannelFactor = 1.0f + (OverChannelTime / Spell.MaxChannelTime) * (ExtendedChannelCostFactor - 1.0f);
        Cost *= OverChannelFactor;
    }
    
    return Cost;
}

bool UMagicStatsFragment::DoesBoostedSpell(const FGameplayTagContainer& SpellTags) const
{
    // Check if the spell has any of the boosted tags
    return SpellTags.HasAny(BoostedSpellTags);
}

void UMagicStatsFragment::ApplyPowerTypeEffect(AActor* Target, EAbilityPowerType AbilityType, float Magnitude)
{
    if (!Target)
    {
        return;
    }
    
    // Apply different effects based on the element
    switch (AbilityType)
    {
        case EAbilityPowerType::Rift:
            // Apply Rift effects (damage over time, debuffs, etc.)
            // This would depend on your game's mechanics
            break;
            
        case EAbilityPowerType::Radiance:
            // Apply Radiance effects (buffs, healing over time, etc.)
            // This would depend on your game's mechanics
            break;
            
        case EAbilityPowerType::Dual:
            // Apply both Rift and Radiance effects
            ApplyPowerTypeEffect(Target, EAbilityPowerType::Rift, Magnitude * 0.7f);
            ApplyPowerTypeEffect(Target, EAbilityPowerType::Radiance, Magnitude * 0.7f);
            break;
            
        default:
            // No elemental effect
            break;
    }
}