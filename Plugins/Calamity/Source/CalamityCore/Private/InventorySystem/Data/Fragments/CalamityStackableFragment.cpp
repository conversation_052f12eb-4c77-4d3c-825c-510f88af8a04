#include "InventorySystem/Data/Fragments/CalamityStackableFragment.h"

#include "InventorySystem/Data/CalamityItemData.h"
#include "InventorySystem/Items/CalamityInventoryItem.h"

UStackableFragment::UStackableFragment()
{
    // Default values
    MaxStackCount = 20;
    InitialStackCount = 1;
    CurrentStackCount = 1;
}

int32 UStackableFragment::AddToStack(int32 Count)
{
    if (Count <= 0)
    {
        return 0;
    }
    
    // Calculate how many items we can actually add
    int32 SpaceAvailable = MaxStackCount - CurrentStackCount;
    int32 ItemsToAdd = FMath::Min(Count, SpaceAvailable);
    
    // Add items
    CurrentStackCount += ItemsToAdd;
    
    // Return how many items were actually added
    return ItemsToAdd;
}

int32 UStackableFragment::RemoveFromStack(int32 Count)
{
    if (Count <= 0)
    {
        return 0;
    }
    
    // Calculate how many items we can actually remove
    int32 ItemsToRemove = FMath::Min(Count, CurrentStackCount);
    
    // Remove items
    CurrentStackCount -= ItemsToRemove;
    
    // Return how many items were actually removed
    return ItemsToRemove;
}

bool UStackableFragment::CanStackWith(UCalamityInventoryItem* OtherItem) const
{
    if (!OtherItem)
    {
        return false;
    }
    
    // Check if the other item has a stackable fragment
    UStackableFragment* OtherStackable = OtherItem->GetFragment<UStackableFragment>();
    if (!OtherStackable)
    {
        return false;
    }
    
    // Check if they have the same item data
    UCalamityInventoryItem* ThisItem = Cast<UCalamityInventoryItem>(GetOuter());
    if (!ThisItem || !ThisItem->GetItemData() || ThisItem->GetItemData() != OtherItem->GetItemData())
    {
        return false;
    }
    
    // Check if there's space in this stack
    if (CurrentStackCount >= MaxStackCount)
    {
        return false;
    }
    
    return true;
}

int32 UStackableFragment::TransferToStack(UCalamityInventoryItem* TargetItem, int32 Count)
{
    if (!TargetItem || Count <= 0)
    {
        return 0;
    }
    
    // Check if the target item has a stackable fragment
    UStackableFragment* TargetStackable = TargetItem->GetFragment<UStackableFragment>();
    if (!TargetStackable)
    {
        return 0;
    }
    
    // Check if they have the same item data
    UCalamityInventoryItem* ThisItem = Cast<UCalamityInventoryItem>(GetOuter());
    if (!ThisItem || !ThisItem->GetItemData() || ThisItem->GetItemData() != TargetItem->GetItemData())
    {
        return 0;
    }
    
    // Calculate how many items we can actually transfer
    int32 ItemsToTransfer = FMath::Min(Count, CurrentStackCount);
    
    // Check how many items the target can accept
    int32 SpaceAvailable = TargetStackable->MaxStackCount - TargetStackable->CurrentStackCount;
    ItemsToTransfer = FMath::Min(ItemsToTransfer, SpaceAvailable);
    
    // Transfer items
    RemoveFromStack(ItemsToTransfer);
    TargetStackable->AddToStack(ItemsToTransfer);
    
    // Return how many items were actually transferred
    return ItemsToTransfer;
}

UCalamityInventoryItem* UStackableFragment::SplitStack(int32 Count)
{
    if (Count <= 0 || Count >= CurrentStackCount)
    {
        return nullptr;
    }
    
    UCalamityInventoryItem* ThisItem = Cast<UCalamityInventoryItem>(GetOuter());
    if (!ThisItem || !ThisItem->GetItemData())
    {
        return nullptr;
    }
    
    // Create a new item instance
    UCalamityInventoryItem* NewInstance = NewObject<UCalamityInventoryItem>(ThisItem->GetOuter());
    NewInstance->InitializeFromItemData(Cast<UCalamityItemData>(ThisItem->GetItemData()));
    
    // Get the stackable fragment of the new instance
    UStackableFragment* NewStackable = NewInstance->GetFragment<UStackableFragment>();
    if (!NewStackable)
    {
        // If the new instance doesn't have a stackable fragment, clean up and return null
        NewInstance->MarkAsGarbage();
        return nullptr;
    }
    
    // Set the stack count
    NewStackable->CurrentStackCount = FMath::Min(Count, CurrentStackCount - 1);
    
    // Remove items from this stack
    RemoveFromStack(NewStackable->CurrentStackCount);
    
    return NewInstance;
}