#include "GameFramework/Systems/Subsystems/CalamityEncounterManagerSubsystem.h"
#include "Components/CalamityEncounterComponent.h"
#include "Interfaces/ICalamityEncounterInterface.h" // Include interface
#include "GameFramework/PlayerState.h"
#include "GameFramework/GameStateBase.h"
#include "Engine/World.h"
#include "DrawDebugHelpers.h"
#include "Engine/Engine.h"
#include "CalamityTags.h"

// Console variables for debugging
static TAutoConsoleVariable<int32> CVarShowEncounterDebug(
	TEXT("Calamity.ShowEncounterDebug"),
	0,
	TEXT("Show encounter system debug visualization\n")
	TEXT("0: Off\n")
	TEXT("1: Basic info\n")
	TEXT("2: Detailed info"),
	ECVF_Cheat
);


void UCalamityEncounterManagerSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);
	UE_LOG(LogTemp, Log, TEXT("CalamityEncounterManagerSubsystem Initialized"));
}

void UCalamityEncounterManagerSubsystem::Deinitialize()
{
	UE_LOG(LogTemp, Log, TEXT("CalamityEncounterManagerSubsystem Deinitialized"));
	RegisteredComponents.Empty();
	ActiveEncounters.Empty();
	SpatialHash.Empty();
	RivalryMap.Empty();
	NemesisHistory.Empty(); // Clear history on deinitialization
	RivalHistory.Empty();
	BatchedAggroEvents.Empty();
	Super::Deinitialize();
}

bool UCalamityEncounterManagerSubsystem::Tick(float DeltaTime)
{
	// Process batched aggro events
	ProcessBatchedAggroEvents();
	
	// Update proximity encounters
	ProcessProximityEncounters(DeltaTime);
	
	// Evaluate and update encounter states
	EvaluateEncounterStates(DeltaTime);
	
	// Decay rivalries over time
	DecayRivalries(DeltaTime);
	
	// Debug visualization
	if (CVarShowEncounterDebug.GetValueOnGameThread() > 0)
	{
		DrawDebugEncounterInfo(DeltaTime);
	}

	return true;
}

// Remove GetStatId if not inheriting from FTickableGameObject
// If you need tick support, inherit from FTickableGameObject and implement this properly.
// Otherwise, just remove this function.

void UCalamityEncounterManagerSubsystem::RegisterEncounterComponent(UCalamityEncounterComponent* Component)
{
	if (Component && !RegisteredComponents.Contains(Component))
	{
		RegisteredComponents.Add(Component);
		// UE_LOG(LogTemp, Log, TEXT("Registered Encounter Component for %s"), *Component->GetOwner()->GetName());
	}
}

void UCalamityEncounterManagerSubsystem::UnregisterEncounterComponent(UCalamityEncounterComponent* Component)
{
	if (Component)
	{
		RegisteredComponents.Remove(Component);
		// UE_LOG(LogTemp, Log, TEXT("Unregistered Encounter Component for %s"), *Component->GetOwner()->GetName());
	}
}

void UCalamityEncounterManagerSubsystem::ProcessGlobalAggroEvent(const FAggroEventData& AggroData)
{
	// Distribute the aggro event to the relevant component(s)
	// This might involve finding the component on the TargetActor or applying AoE aggro

	if (!AggroData.TargetActor.IsValid())
		return;

	if (AggroData.TargetActor.IsValid())
	{
		ICalamityEncounterInterface* EncounterTarget = Cast<ICalamityEncounterInterface>(AggroData.TargetActor.Get());
		if (EncounterTarget)
		{
			// Use Execute_ to call interface functions safely
			ICalamityEncounterInterface::Execute_AddAggro(AggroData.TargetActor.Get(), AggroData);
		}
		else // Check component directly if interface isn't on actor
		{
			UCalamityEncounterComponent* TargetComponent = AggroData.TargetActor->FindComponentByClass<UCalamityEncounterComponent>();
			if (TargetComponent)
			{
				// Call implementation directly (less flexible than interface)
				TargetComponent->AddAggro_Implementation(AggroData);
			}
		}
	}
	else
	{
		// Handle AoE aggro or events without a direct target?
		// Maybe iterate registered components and check proximity/conditions?
	}

	// TODO: Potentially trigger encounter evaluations based on significant aggro events
}

void UCalamityEncounterManagerSubsystem::ProcessBatchedAggroEvents()
{
	for (auto& Pair : BatchedAggroEvents)
	{
		uint32 PlayerID = Pair.Key;
		FBatchedAggroEvent& Batch = Pair.Value;
		
		if (Batch.Events.Num() > 0)
		{
			// Find the player's component
			for (auto& CompPtr : RegisteredComponents)
			{
				if (UCalamityEncounterComponent* Comp = CompPtr.Get())
				{
					if (APawn* Pawn = Cast<APawn>(Comp->GetOwner()))
					{
						if (APlayerState* PS = Pawn->GetPlayerState())
						{
							if (GetPlayerUniqueId(PS) == PlayerID)
							{
								// Create aggregated event
								FAggroEventData AggregatedEvent;
								AggregatedEvent.BaseAggroAmount = Batch.TotalAggro;
								AggregatedEvent.EventTags = Batch.CombinedTags;
								
								// Use the first event's data for other fields
								if (Batch.Events.Num() > 0)
								{
									AggregatedEvent.InstigatorActor = Batch.Events[0].InstigatorActor;
									AggregatedEvent.TargetActor = Batch.Events[0].TargetActor;
									AggregatedEvent.WorldLocation = Batch.Events[0].WorldLocation;
								}
								
								// Send aggregated event
								ICalamityEncounterInterface::Execute_AddAggro(Comp->GetOwner(), AggregatedEvent);
								break;
							}
						}
					}
				}
			}
		}
	}
	
	// Clear batches
	BatchedAggroEvents.Empty();
}

void UCalamityEncounterManagerSubsystem::UpdatePlayerProximity(APlayerState* PlayerA, APlayerState* PlayerB, float Distance, bool bInCombat)
{
	if (!PlayerA || !PlayerB || PlayerA == PlayerB)
		return;
	
	uint32 PlayerAID = GetPlayerUniqueId(PlayerA);
	uint32 PlayerBID = GetPlayerUniqueId(PlayerB);
	
	if (PlayerAID == 0 || PlayerBID == 0)
		return;
	
	// Update spatial hash
	if (AActor* PawnA = PlayerA->GetPawn())
	{
		UpdateSpatialHash(PlayerAID, PawnA->GetActorLocation());
	}
	
	// Track proximity in rivalry data
	if (Distance <= ProximityThreshold)
	{
		FRivalMapWrapper& RivalsWrapper = RivalryMap.FindOrAdd(PlayerAID);
		FRivalData& RivalData = RivalsWrapper.VictimMap.FindOrAdd(PlayerBID);
		
		float DeltaTime = GetWorld()->GetDeltaSeconds();
		RivalData.ProximityData.TimeInProximity += DeltaTime;
		
		if (bInCombat)
		{
			RivalData.ProximityData.CombatTimeInProximity += DeltaTime * CombatProximityMultiplier;
			RivalData.ProximityData.EngagementCount++;
			RivalData.ProximityData.LastEngagementTime = FDateTime::Now();
		}
		
		// Update rivalry intensity based on proximity
		UpdateRivalryIntensity(RivalData);
	}
}

void UCalamityEncounterManagerSubsystem::NotifyObjectiveContest(APlayerState* PlayerA, APlayerState* PlayerB, const FGameplayTag& ObjectiveTag, APlayerState* Winner)
{
	if (!PlayerA || !PlayerB || PlayerA == PlayerB)
		return;
	
	uint32 PlayerAID = GetPlayerUniqueId(PlayerA);
	uint32 PlayerBID = GetPlayerUniqueId(PlayerB);
	
	// Update both players' rivalry data
	auto UpdateObjectiveRivalry = [&](uint32 PlayerID, uint32 RivalID, bool bWon)
	{
		FRivalMapWrapper& RivalsWrapper = RivalryMap.FindOrAdd(PlayerID);
		FRivalData& RivalData = RivalsWrapper.VictimMap.FindOrAdd(RivalID);
		
		FObjectiveRivalryData& ObjRivalry = RivalData.ObjectiveRivalries.FindOrAdd(ObjectiveTag);
		ObjRivalry.ObjectiveTag = ObjectiveTag;
		ObjRivalry.ContestCount++;
		
		if (bWon)
			ObjRivalry.PlayerWins++;
		else
			ObjRivalry.RivalWins++;
		
		// Update rivalry intensity
		UpdateRivalryIntensity(RivalData);
	};
	
	UpdateObjectiveRivalry(PlayerAID, PlayerBID, Winner == PlayerA);
	UpdateObjectiveRivalry(PlayerBID, PlayerAID, Winner == PlayerB);
}

ERivalryIntensity UCalamityEncounterManagerSubsystem::GetRivalryIntensity(APlayerState* PlayerA, APlayerState* PlayerB) const
{
	if (!PlayerA || !PlayerB)
		return ERivalryIntensity::None;
	
	uint32 PlayerAID = GetPlayerUniqueId(PlayerA);
	uint32 PlayerBID = GetPlayerUniqueId(PlayerB);
	
	if (const FRivalMapWrapper* Wrapper = RivalryMap.Find(PlayerAID))
	{
		if (const FRivalData* RivalData = Wrapper->VictimMap.Find(PlayerBID))
		{
			return RivalData->RivalryIntensity;
		}
	}
	
	return ERivalryIntensity::None;
}

TArray<FRivalData> UCalamityEncounterManagerSubsystem::GetTopRivals(APlayerState* ForPlayer, int32 MaxCount) const
{
	return TArray<FRivalData>();
}

void UCalamityEncounterManagerSubsystem::StartEncounter(const TArray<APlayerState*>& Participants, bool bNearObjective, const FGameplayTag& ObjectiveTag)
{
	if (Participants.Num() < 2)
		return;
	
	FActiveEncounter NewEncounter;
	NewEncounter.StartTime = FDateTime::Now();
	NewEncounter.CurrentState = EEncounterState::Engaging;
	NewEncounter.bIsNearObjective = bNearObjective;
	NewEncounter.NearbyObjectiveTag = ObjectiveTag;
	
	// Calculate encounter center
	FVector CenterSum = FVector::ZeroVector;
	int32 ValidPawns = 0;
	
	for (APlayerState* PS : Participants)
	{
		if (PS)
		{
			uint32 PlayerID = GetPlayerUniqueId(PS);
			NewEncounter.ParticipantIDs.Add(PlayerID);
			NewEncounter.ParticipantScores.Add(PlayerID, FEncounterScore());
			
			if (AActor* Pawn = PS->GetPawn())
			{
				CenterSum += Pawn->GetActorLocation();
				ValidPawns++;
			}
		}
	}
	
	if (ValidPawns > 0)
	{
		NewEncounter.EncounterCenter = CenterSum / ValidPawns;
	}
	
	int32 EncounterID = NextEncounterID++;
	ActiveEncounters.Add(EncounterID, NewEncounter);
	
	// Notify participants
	bool bIsOneVsOne = (Participants.Num() == 2);
	TArray<AActor*> ParticipantActors;
	
	for (APlayerState* PS : Participants)
	{
		if (AActor* Pawn = PS->GetPawn())
		{
			ParticipantActors.Add(Pawn);
			
			if (UCalamityEncounterComponent* Comp = Pawn->FindComponentByClass<UCalamityEncounterComponent>())
			{
				ICalamityEncounterInterface::Execute_OnEncounterStarted(Pawn, bIsOneVsOne, ParticipantActors);
			}
		}
	}
}

void UCalamityEncounterManagerSubsystem::UpdateEncounterState(int32 EncounterID, EEncounterState NewState)
{
}

void UCalamityEncounterManagerSubsystem::EndEncounter(int32 EncounterID)
{
}

void UCalamityEncounterManagerSubsystem::UpdateEncounterScore(int32 EncounterID, APlayerState* Player, const FEncounterScore& ScoreUpdate)
{
	if (FActiveEncounter* Encounter = ActiveEncounters.Find(EncounterID))
	{
		uint32 PlayerID = GetPlayerUniqueId(Player);
		if (FEncounterScore* Score = Encounter->ParticipantScores.Find(PlayerID))
		{
			// Accumulate scores
			Score->DamageDealt += ScoreUpdate.DamageDealt;
			Score->DamageTaken += ScoreUpdate.DamageTaken;
			Score->HealingDone += ScoreUpdate.HealingDone;
			Score->ObjectiveContribution += ScoreUpdate.ObjectiveContribution;
			Score->TeamplayScore += ScoreUpdate.TeamplayScore;
			Score->KillStreak = FMath::Max(Score->KillStreak, ScoreUpdate.KillStreak);
			Score->SkillRating = FMath::Max(Score->SkillRating, ScoreUpdate.SkillRating);
		}
	}
}

FEncounterScore UCalamityEncounterManagerSubsystem::GetEncounterScore(int32 EncounterID, APlayerState* Player) const
{
	return FEncounterScore();
}

float UCalamityEncounterManagerSubsystem::GetEncounterIntensity(int32 EncounterID) const
{
	if (const FActiveEncounter* Encounter = ActiveEncounters.Find(EncounterID))
	{
		float Intensity = 0.0f;
		
		// Calculate based on participant scores and threat levels
		for (const auto& ScorePair : Encounter->ParticipantScores)
		{
			const FEncounterScore& Score = ScorePair.Value;
			float ParticipantIntensity = Score.CalculateTotalScore() / 100.0f;
			
			// Check threat level
			for (auto& CompPtr : RegisteredComponents)
			{
				if (UCalamityEncounterComponent* Comp = CompPtr.Get())
				{
					if (APawn* Pawn = Cast<APawn>(Comp->GetOwner()))
					{
						if (APlayerState* PS = Pawn->GetPlayerState())
						{
							if (GetPlayerUniqueId(PS) == ScorePair.Key)
							{
								EThreatLevel ThreatLevel = ICalamityEncounterInterface::Execute_GetCurrentThreatLevel(Comp->GetOwner());
								ParticipantIntensity *= (1.0f + (float)ThreatLevel * 0.2f);
								break;
							}
						}
					}
				}
			}
			
			Intensity += ParticipantIntensity;
		}
		
		// Normalize by participant count
		if (Encounter->ParticipantIDs.Num() > 0)
		{
			Intensity /= Encounter->ParticipantIDs.Num();
		}
		
		// Boost if near objective
		if (Encounter->bIsNearObjective)
		{
			Intensity *= 1.5f;
		}
		
		return FMath::Clamp(Intensity, 0.0f, 1.0f);
	}
	
	return 0.0f;
}

bool UCalamityEncounterManagerSubsystem::ShouldTriggerRivalryVO(APlayerState* PlayerA, APlayerState* PlayerB) const
{
	ERivalryIntensity Intensity = GetRivalryIntensity(PlayerA, PlayerB);
	
	// Trigger VO for heated rivalries or higher
	if (Intensity >= ERivalryIntensity::Heated)
	{
		// Additional checks for cooldowns, etc.
		return true;
	}
	
	return false;
}

void UCalamityEncounterManagerSubsystem::NotifyTeamEncounter(const TArray<APlayerState*>& TeamA, const TArray<APlayerState*>& TeamB)
{
	// Combine both teams for encounter
	TArray<APlayerState*> AllParticipants;
	AllParticipants.Append(TeamA);
	AllParticipants.Append(TeamB);
	
	StartEncounter(AllParticipants, false, FGameplayTag());
	
	// Track team-based rivalries
	for (APlayerState* PlayerA : TeamA)
	{
		for (APlayerState* PlayerB : TeamB)
		{
			UpdatePlayerProximity(PlayerA, PlayerB, 0.0f, true);
		}
	}
}

float UCalamityEncounterManagerSubsystem::GetTeamThreatLevel(int32 TeamID) const
{
	float TotalThreat = 0.0f;
	int32 TeamMemberCount = 0;
	
	// Sum threat levels of all team members
	for (auto& CompPtr : RegisteredComponents)
	{
		if (UCalamityEncounterComponent* Comp = CompPtr.Get())
		{
			if (APawn* Pawn = Cast<APawn>(Comp->GetOwner()))
			{
				if (APlayerState* PS = Pawn->GetPlayerState())
				{
					//if (PS->GetTeamNum() == TeamID)
					//{
						EThreatLevel ThreatLevel = ICalamityEncounterInterface::Execute_GetCurrentThreatLevel(Comp->GetOwner());
						TotalThreat += (float)ThreatLevel;
						TeamMemberCount++;
					//}
				}
			}
		}
	}
	
	// Return average threat level
	return TeamMemberCount > 0 ? TotalThreat / TeamMemberCount : 0.0f;
}

void UCalamityEncounterManagerSubsystem::ToggleDebugVisualization()
{
	bEnableDebugVisualization = !bEnableDebugVisualization;
}

void UCalamityEncounterManagerSubsystem::DrawDebugEncounterInfo(float DeltaTime)
{
	if (!GetWorld() || !bEnableDebugVisualization)
		return;

	const float TextHeight = 40.0f;
	const float SphereRadius = 300.0f;
	const FColor EncounterColor = FColor::Orange;
	const FColor RivalColor = FColor::Red;
	const FColor NemesisColor = FColor::Purple;

	// Draw all active encounters
	for (const auto& Pair : ActiveEncounters)
	{
		int32 EncounterID = Pair.Key;
		const FActiveEncounter& Encounter = Pair.Value;

		// Draw encounter center
		DrawDebugSphere(GetWorld(), Encounter.EncounterCenter, SphereRadius, 16, EncounterColor, false, -1.0f, 0, 2.0f);

		// Draw encounter info text
		FString Info = FString::Printf(TEXT("Encounter %d\nState: %s\nParticipants: %d\nNearObj: %s"),
			EncounterID,
			*UEnum::GetValueAsString(Encounter.CurrentState),
			Encounter.ParticipantIDs.Num(),
			Encounter.bIsNearObjective ? *Encounter.NearbyObjectiveTag.ToString() : TEXT("No"));

		DrawDebugString(GetWorld(), Encounter.EncounterCenter + FVector(0,0,TextHeight), Info, nullptr, EncounterColor, DeltaTime, false);

		// Draw lines to participants
		for (uint32 PlayerID : Encounter.ParticipantIDs)
		{
			for (auto& CompPtr : RegisteredComponents)
			{
				if (UCalamityEncounterComponent* Comp = CompPtr.Get())
				{
					if (APawn* Pawn = Cast<APawn>(Comp->GetOwner()))
					{
						if (APlayerState* PS = Pawn->GetPlayerState())
						{
							if (GetPlayerUniqueId(PS) == PlayerID)
							{
								DrawDebugLine(GetWorld(), Encounter.EncounterCenter, Pawn->GetActorLocation(), EncounterColor, false, DeltaTime, 0, 2.0f);
								DrawDebugString(GetWorld(), Pawn->GetActorLocation() + FVector(0,0,TextHeight/2), PS->GetPlayerName(), nullptr, EncounterColor, DeltaTime, false);
							}
						}
					}
				}
			}
		}
	}

	// Draw Rivalries (optional: only for high intensity)
	for (const auto& RivalPair : RivalryMap)
	{
		uint32 PlayerAID = RivalPair.Key;
		const FRivalMapWrapper& Wrapper = RivalPair.Value;
		for (const auto& VictimPair : Wrapper.VictimMap)
		{
			uint32 PlayerBID = VictimPair.Key;
			const FRivalData& RivalData = VictimPair.Value;
			if (RivalData.RivalryIntensity >= ERivalryIntensity::Heated)
			{
				AActor* PawnA = nullptr;
				AActor* PawnB = nullptr;
				for (auto& CompPtr : RegisteredComponents)
				{
					if (UCalamityEncounterComponent* Comp = CompPtr.Get())
					{
						if (APawn* Pawn = Cast<APawn>(Comp->GetOwner()))
						{
							if (APlayerState* PS = Pawn->GetPlayerState())
							{
								if (GetPlayerUniqueId(PS) == PlayerAID)
									PawnA = Pawn;
								if (GetPlayerUniqueId(PS) == PlayerBID)
									PawnB = Pawn;
							}
						}
					}
				}
				if (PawnA && PawnB)
				{
					DrawDebugLine(GetWorld(), PawnA->GetActorLocation(), PawnB->GetActorLocation(), RivalColor, false, DeltaTime, 0, 4.0f);
					FString RivalText = FString::Printf(TEXT("Rivalry: %s\nKills: %d Deaths: %d"),
						*UEnum::GetValueAsString(RivalData.RivalryIntensity),
						RivalData.KillsAgainstRival,
						RivalData.DeathsByRival);
					DrawDebugString(GetWorld(), (PawnA->GetActorLocation() + PawnB->GetActorLocation()) / 2 + FVector(0,0,TextHeight), RivalText, nullptr, RivalColor, DeltaTime, false);
				}
			}
		}
	}

	// Draw Nemesis history (optional)
	for (const FNemesisData& Nemesis : NemesisHistory)
	{
		// Find pawn for NemesisID if possible (requires mapping)
		// Draw text somewhere on screen or at a fixed location
		FVector Location = FVector(0, 0, 500 + 50 * (&Nemesis - NemesisHistory.GetData()));
		FString NemesisText = FString::Printf(TEXT("Nemesis: %s\nEncounters: %d\nWins: %d Losses: %d\nLast: %s"),
			*Nemesis.NemesisID, Nemesis.Encounters, Nemesis.PlayerWins, Nemesis.NemesisWins, *Nemesis.LastEncounterOutcome);
		DrawDebugString(GetWorld(), Location, NemesisText, nullptr, NemesisColor, DeltaTime, false);
	}
}

void UCalamityEncounterManagerSubsystem::UpdateRivalData(FString RivalID, bool bIsPlayerRival, bool bPlayerWon, FGameplayTagContainer EncounterTags)
{
}

FRivalData UCalamityEncounterManagerSubsystem::GetRivalData(FString RivalID) const
{
	return FRivalData();
}
FNemesisData UCalamityEncounterManagerSubsystem::GetNemesisData(FString NemesisID) const
{
	return FNemesisData();
}

void UCalamityEncounterManagerSubsystem::NotifyPlayerKill(APlayerState* KillerPlayerState, APlayerState* VictimPlayerState)
{
	if (!KillerPlayerState || !VictimPlayerState || KillerPlayerState == VictimPlayerState)
	{
		return; // Invalid data or self-kill
	}

	uint32 KillerId = GetPlayerUniqueId(KillerPlayerState);
	uint32 VictimId = GetPlayerUniqueId(VictimPlayerState);

	if (KillerId == 0 || VictimId == 0)
	{
		UE_LOG(LogTemp, Warning, TEXT("NotifyPlayerKill: Invalid PlayerState Unique ID(s)."));
		return;
	}
	
	// Update Killer's Data against Victim
	// 1. Find or Add the wrapper for the Killer
	FRivalMapWrapper& KillerRivalsWrapper = RivalryMap.FindOrAdd(KillerId);
	// 2. Access the VictimMap within the wrapper and Find or Add the RivalData for the Victim
	FRivalData& KillerVsVictimData = KillerRivalsWrapper.VictimMap.FindOrAdd(VictimId);
	KillerVsVictimData.KillsAgainstRival++;

	// Update Victim's Data against Killer
	// 1. Find or Add the wrapper for the Victim
	FRivalMapWrapper& VictimRivalsWrapper = RivalryMap.FindOrAdd(VictimId);
	// 2. Access the VictimMap within the wrapper and Find or Add the RivalData for the Killer
	FRivalData& VictimVsKillerData = VictimRivalsWrapper.VictimMap.FindOrAdd(KillerId);
	VictimVsKillerData.DeathsByRival++;
	// --- End Corrected Access Logic ---

	// Check and update Rival status for both perspectives
	UpdateRivalryBetweenPlayers(KillerPlayerState, VictimPlayerState);
	UpdateRivalryBetweenPlayers(VictimPlayerState, KillerPlayerState); // Check from the other perspective too

	// TODO: Potentially add Perks based on kill events (e.g., "Shutdown" if killing high-threat enemy)
	// TODO: Trigger Rival-specific dialogue or events
}

void UCalamityEncounterManagerSubsystem::UpdateNemesisRecord(FString NemesisID, bool bPlayerWon)
{
	if (FNemesisData* Data = NemesisHistory.FindByPredicate([&](const FNemesisData& Elem) { return Elem.NemesisID == NemesisID; }))
    {
        Data->Encounters++;
        if (bPlayerWon)
        {
            Data->PlayerWins++;
        }
        else
        {
            Data->NemesisWins++;
        }
        Data->LastEncounterOutcome = bPlayerWon ? TEXT("Win") : TEXT("Loss");
    }
    else
    {
        FNemesisData NewData;
        NewData.NemesisID = NemesisID;
        NewData.Encounters = 1;
        NewData.PlayerWins = bPlayerWon ? 1 : 0;
        NewData.NemesisWins = bPlayerWon ? 0 : 1;
        NewData.LastEncounterOutcome = bPlayerWon ? TEXT("Win") : TEXT("Loss");
        NemesisHistory.Add(NewData);
    }
}

void UCalamityEncounterManagerSubsystem::UpdateNemesisData(FString NemesisID, bool bPlayerWon)
{
}

FNemesisData UCalamityEncounterManagerSubsystem::GetNemesisRecord(FString NemesisID) const
{
    const FNemesisData* Data = NemesisHistory.FindByPredicate([&](const FNemesisData& Elem) { return Elem.NemesisID == NemesisID; });
    return Data ? *Data : FNemesisData();
}

void UCalamityEncounterManagerSubsystem::UpdateRivalRecord(FString RivalID, bool bIsPlayerRival, bool bPlayerWon, FGameplayTagContainer EncounterTags)
{
    FRivalData* Data = RivalHistory.FindByPredicate([&](const FRivalData& Elem) { return Elem.RivalID == RivalID; });
    if (Data)
    {
        Data->TimesEncountered++;
        if (bPlayerWon)
        {
            Data->PlayerWins++;
        }
        else
        {
            Data->RivalWins++;
        }
        Data->LastEncounterOutcome = bPlayerWon 
            ? FGameplayTagContainer(FGameplayTag::RequestGameplayTag(TEXT("Encounter.Outcome.Won"))) 
            : FGameplayTagContainer(FGameplayTag::RequestGameplayTag(TEXT("Encounter.Outcome.Loss")));
        Data->LastEncounterTags = EncounterTags;
    }
    else
    {
        FRivalData NewData;
        NewData.RivalID = RivalID;
        NewData.bIsPlayerRival = bIsPlayerRival;
        NewData.TimesEncountered = 1;
        NewData.PlayerWins = bPlayerWon ? 1 : 0;
        NewData.RivalWins = bPlayerWon ? 0 : 1;
        NewData.LastEncounterOutcome = bPlayerWon 
            ? FGameplayTagContainer(FGameplayTag::RequestGameplayTag(TEXT("Encounter.Outcome.Won"))) 
            : FGameplayTagContainer(FGameplayTag::RequestGameplayTag(TEXT("Encounter.Outcome.Loss")));
        NewData.LastEncounterTags = EncounterTags;
        RivalHistory.Add(NewData);
    }
}

FRivalData UCalamityEncounterManagerSubsystem::GetRivalRecord(FString RivalID) const
{
    const FRivalData* Data = RivalHistory.FindByPredicate([&](const FRivalData& Elem) { return Elem.RivalID == RivalID; });
    return Data ? *Data : FRivalData();
}

// --- Internal Helper Functions ---

void UCalamityEncounterManagerSubsystem::UpdateRivalryBetweenPlayers(APlayerState* PlayerA, APlayerState* PlayerB)
{
	if (!PlayerA || !PlayerB) return;

	uint32 PlayerAId = GetPlayerUniqueId(PlayerA);
	uint32 PlayerBId = GetPlayerUniqueId(PlayerB);

	if (PlayerAId == 0 || PlayerBId == 0) return;
	
	// 1. Find the wrapper for Player A using Find (returns pointer)
	//    Make sure PlayerARivalsWrapper is a pointer to the wrapper struct.
	if (FRivalMapWrapper* PlayerARivalsWrapper = RivalryMap.Find(PlayerAId)) // This line finds the FRivalMapWrapper*
	{
		// 2. Find the RivalData for Player B within the wrapper's VictimMap using Find (returns pointer)
		//    Access the VictimMap member of the found wrapper.
		if (FRivalData* RivalData = PlayerARivalsWrapper->VictimMap.Find(PlayerBId)) // Access VictimMap here
		{
			// 3. Now you have the correct FRivalData*, proceed with logic
			bool bShouldBeRival = CheckRivalryCriteria(*RivalData);

			// Notify Player A about their Rival status with Player B
			AActor* PawnA = PlayerA->GetPawn(); // Get Pawn for interface call
			AActor* PawnB = PlayerB->GetPawn();
			if(!PawnA || !PawnB) return; // Need valid pawns

			ICalamityEncounterInterface* EncounterInterfaceA = Cast<ICalamityEncounterInterface>(PawnA);
			if (EncounterInterfaceA)
			{
				// Ensure the interface function exists and is called correctly
				ICalamityEncounterInterface::Execute_UpdateRivalStatus(PawnA, PawnB, bShouldBeRival);
			}
			else // Check component directly
			{
				UCalamityEncounterComponent* CompA = PawnA->FindComponentByClass<UCalamityEncounterComponent>();
				if (CompA)
				{
					// Ensure the implementation function exists and is called correctly
					CompA->UpdateRivalStatus_Implementation(PawnB, bShouldBeRival);
				}
			}
		}
		// else: Player A has records, but not specifically for Player B yet.
	}
	// else: Player A has no rivalry records at all yet.
}

bool UCalamityEncounterManagerSubsystem::CheckRivalryCriteria(const FRivalData& Data) const
{
	// TODO: Implement actual logic based on kills, deaths, streaks, etc.
	// Using config values defined in header
	if (Data.KillsAgainstRival >= RivalryKillThreshold || Data.DeathsByRival >= RivalryKillThreshold)
	{
		return true;
	}
	// Add streak check logic if needed (requires tracking kill timestamps or sequences)

	return false;
}

uint32 UCalamityEncounterManagerSubsystem::GetPlayerUniqueId(APlayerState* PlayerState) const
{
	// PlayerState->GetUniqueID() is persistent within a session
	return PlayerState ? PlayerState->GetUniqueID() : 0;
}

FIntVector UCalamityEncounterManagerSubsystem::GetSpatialHashKey(const FVector& Location) const
{
	return FIntVector();
}

void UCalamityEncounterManagerSubsystem::UpdateSpatialHash(uint32 PlayerID, const FVector& Location)
{
}

TArray<uint32> UCalamityEncounterManagerSubsystem::GetNearbyPlayers(const FVector& Location, float Radius) const
{
	return TArray<uint32>();
}

void UCalamityEncounterManagerSubsystem::UpdateRivalryIntensity(FRivalData& RivalData)
{
}

void UCalamityEncounterManagerSubsystem::DecayRivalries(float DeltaTime)
{
}

float UCalamityEncounterManagerSubsystem::CalculateRivalryScore(const FRivalData& RivalData) const
{
	return 0.0f;
}

void UCalamityEncounterManagerSubsystem::ProcessProximityEncounters(float DeltaTime)
{
}

void UCalamityEncounterManagerSubsystem::EvaluateEncounterStates(float DeltaTime)
{
}

// Implement GenerateRivalID / GenerateNemesisID if they are needed here
// FString UCalamityEncounterManagerSubsystem::GenerateRivalID(...) { ... }
// FString UCalamityEncounterManagerSubsystem::GenerateNemesisID(...) { ... }
