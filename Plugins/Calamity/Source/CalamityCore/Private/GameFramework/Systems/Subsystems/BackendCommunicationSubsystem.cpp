// Fill out your copyright notice in the Description page of Project Settings.

#include "GameFramework/Systems/Subsystems/BackendCommunicationSubsystem.h"

/*
void UBackendCommunicationSubsystem::SendPlayerStats(const FPlayerStatsData& Stats)
{
}

void UBackendCommunicationSubsystem::RequestPlayerRating(const FString& PlayerId)
{
}

void UBackendCommunicationSubsystem::ConnectToMatchmakingService()
{
}

TSharedPtr<class IHttpRequest> UBackendCommunicationSubsystem::CreateHttpRequest()
{
}
*/
