// Fill out your copyright notice in the Description page of Project Settings.

#include "GameFramework/Systems/Subsystems/MatchmakingSubsystem.h"

void UMatchmakingSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);
}

void UMatchmakingSubsystem::Deinitialize()
{
	Super::Deinitialize();
}

void UMatchmakingSubsystem::JoinMatchmakingQueue(const FString& GameMode)
{
}

void UMatchmakingSubsystem::LeaveMatchmakingQueue()
{
}
