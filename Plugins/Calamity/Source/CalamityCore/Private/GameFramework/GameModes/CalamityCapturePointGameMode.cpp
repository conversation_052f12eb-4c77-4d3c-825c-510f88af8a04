// Fill out your copyright notice in the Description page of Project Settings.

#include "GameFramework/GameModes/CalamityCapturePointGameMode.h"
#include "GameFramework/States/Game/CalamityCapturePointGameState.h"
#include "Characters/CalamityCharacter.h"
#include "Controller/CalamityPlayerController.h"
#include "OnlineSubsystem.h"
#include "Interfaces/OnlineStatsInterface.h"
#include "Interfaces/OnlineIdentityInterface.h"
#include "Interfaces/OnlineLeaderboardInterface.h"
#include "GameFramework/States/Player/CalamityPlayerState.h"

/*
 * Kills/Deaths: In your game mode's logic where you handle player death (e.g., in response to ApplyDamage or a dedicated PlayerDied function),
 * get the APlayerState of the victim and the killer (if applicable) and call their respective RecordDeath() and RecordKill() functions.
 * Match End (Wins/Losses): When the match ends (timer runs out, objective completed), the AGameModeBase determines the winner/loser. You'll use this outcome later when saving persistent stats.
 * This is where you save lifetime stats. The server (AGameModeBase) is responsible for initiating the save process at the end of a match.
 */

ACalamityCapturePointGameMode::ACalamityCapturePointGameMode()
{
    // Set default classes
    GameStateClass = ACalamityCapturePointGameState::StaticClass();
}

void ACalamityCapturePointGameMode::BeginPlay()
{
    Super::BeginPlay();
}

// STATS
// In YourGameMode.cpp (Example in a hypothetical HandlePlayerDeath function)
void ACalamityCapturePointGameMode::HandlePlayerDeath(AController* KillerController, AController* VictimController)
{
    if (!HasAuthority()) return; // Server only

    ACalamityPlayerState* VictimPlayerState = VictimController ? VictimController->GetPlayerState<ACalamityPlayerState>() : nullptr;
    if (VictimPlayerState)
    {
        VictimPlayerState->RecordDeath();
    }

    ACalamityPlayerState* KillerPlayerState = KillerController ? KillerController->GetPlayerState<ACalamityPlayerState>() : nullptr;
    if (KillerPlayerState && KillerPlayerState != VictimPlayerState) // Check for valid killer and not suicide
    {
        KillerPlayerState->RecordKill();
        // You might also handle assists here by checking damage history etc.
    }

    // Potentially update GameState team scores here too
    // AYourGameState* GS = GetGameState<AYourGameState>();
    // if (GS) { GS->UpdateTeamScore(...); }
}

/*
* Unreal Online Subsystems (OSS): (Recommended for robust online games)

Provides interfaces for interacting with platform services (Steam, Epic Online Services (EOS), Xbox Live, PSN) or a generic backend.
Key Interfaces:
IOnlineStats: For writing/reading player statistics.
IOnlineLeaderboards: For writing scores to leaderboards.
IOnlineIdentity: To get the unique player ID required for stats/leaderboards.
IOnlineUserCloud (Optional): For saving larger blobs of data like player loadouts or raw stat history if needed.
Pros: Platform integration, standard UE way, handles auth.
Cons: Requires setup per platform (DefaultEngine.ini configuration), asynchronous operations require careful handling (callbacks/delegates).
Custom Backend API:

You create your own web server (e.g., using Node.js, Python/Flask/Django, C# ASP.NET) with a database (SQL or NoSQL).
UE communicates with it via HTTP requests (using UE's FHttpModule).
Pros: Full control over data, logic, and platform independence.
Cons: Significant development effort (backend, API, security, scaling), requires infrastructure.
Local USaveGame Object:

Only suitable for single-player games or offline stats.
Cannot be used for online leaderboards or shared persistent stats in multiplayer.
Simple to implement using UGameplayStatics::SaveGameToSlot/LoadGameFromSlot.
Implementation Steps (Conceptual - Using OSS):

Get OSS Interfaces: In your AGameModeBase or a dedicated C++ subsystem (UGameInstanceSubsystem is good for managing OSS interactions).
 */

/*#include "OnlineSubsystem.h"
#include "Interfaces/OnlineStatsInterface.h"
#include "Interfaces/OnlineIdentityInterface.h"
#include "Interfaces/OnlineLeaderboardInterface.h"

void AYourGameMode::SavePersistentStats() // Call this at the end of the match on the server
{
    if (!HasAuthority()) return;

    IOnlineSubsystem* OnlineSub = IOnlineSubsystem::Get();
    if (!OnlineSub)
    {
        UE_LOG(LogTemp, Warning, TEXT("No Online Subsystem found!"));
        return;
    }

    IOnlineIdentityPtr IdentityInt = OnlineSub->GetIdentityInterface();
    IOnlineStatsPtr StatsInt = OnlineSub->GetStatsInterface();
    // IOnlineLeaderboardsPtr LeaderboardInt = OnlineSub->GetLeaderboardsInterface(); // For leaderboards

    if (!IdentityInt.IsValid() || !StatsInt.IsValid() /* || !LeaderboardInt.IsValid() #1#)
    {
         UE_LOG(LogTemp, Warning, TEXT("Required Online Interfaces not available."));
        return;
    }

    bool bMatchWon = DetermineMatchOutcome(); // Your logic to check if the local player's team won

    // Iterate through all players who participated in the match
    for (APlayerState* PlayerState : GetGameState<AGameStateBase>()->PlayerArray)
    {
        AYourPlayerState* YourPS = Cast<AYourPlayerState>(PlayerState);
        if (!YourPS) continue;

        ULocalPlayer* LocalPlayer = YourPS->GetPlayerController() ? YourPS->GetPlayerController()->GetLocalPlayer() : nullptr;
        if(!LocalPlayer) continue; // Need LocalPlayer for Identity

        TSharedPtr<const FUniqueNetId> UserId = IdentityInt->GetUniquePlayerId(LocalPlayer->GetControllerId());
        if (!UserId.IsValid())
        {
            UE_LOG(LogTemp, Warning, TEXT("Could not get UniqueNetId for player %s"), *YourPS->GetPlayerName());
            continue;
        }

        // --- 1. Read Existing Stats (Asynchronous) ---
        // You usually need to read existing stats first to update them.
        // StatsInt->QueryStats(...); // Requires setting up a delegate to handle the response

        // --- 2. Prepare Stat Updates ---
        // Assuming you have FPlayerPersistentStats CurrentPersistentStats loaded/read somehow
        FPlayerPersistentStats UpdatedStats = CurrentPersistentStats; // Start with current values
        UpdatedStats.MergeMatchStats(YourPS->MatchStats, bMatchWon); // Update with match results

        // --- 3. Write Updated Stats (Asynchronous) ---
        TArray<FOnlineStatsUserUpdatedStats> PlayerStatsToWrite;
        FOnlineStatsUserUpdatedStats& UserStats = PlayerStatsToWrite.Emplace_GetRef(*UserId);

        // Map your FPlayerPersistentStats members to named stats recognized by the OSS/backend
        UserStats.Stats.Emplace(TEXT("LifetimeKills"), FOnlineStatUpdate(UpdatedStats.LifetimeKills, FOnlineStatUpdate::EOnlineStatModification::Set));
        UserStats.Stats.Emplace(TEXT("LifetimeDeaths"), FOnlineStatUpdate(UpdatedStats.LifetimeDeaths, FOnlineStatUpdate::EOnlineStatModification::Set));
        UserStats.Stats.Emplace(TEXT("MatchesWon"), FOnlineStatUpdate(UpdatedStats.MatchesWon, FOnlineStatUpdate::EOnlineStatModification::Set));
        UserStats.Stats.Emplace(TEXT("MatchesLost"), FOnlineStatUpdate(UpdatedStats.MatchesLost, FOnlineStatUpdate::EOnlineStatModification::Set));
        UserStats.Stats.Emplace(TEXT("MatchesPlayed"), FOnlineStatUpdate(UpdatedStats.MatchesPlayed, FOnlineStatUpdate::EOnlineStatModification::Set));
        // ... Add other stats

        // Create a delegate to handle the write completion
        FOnlineStatsUpdateStatsComplete Delegate = FOnlineStatsUpdateStatsComplete::CreateUObject(this, &AYourGameMode::OnStatsWritten);
        StatsInt->UpdateStats(UserId.ToSharedRef(), PlayerStatsToWrite, Delegate);

        // --- 4. Write Leaderboard Scores (Optional, Asynchronous) ---
        // if (LeaderboardInt.IsValid())
        // {
        //     FOnlineLeaderboardWrite WriteObject;
        //     WriteObject.LeaderboardNames.Add(FName(TEXT("GlobalKillsLeaderboard"))); // Example leaderboard name
        //     WriteObject.RatedStat = FName(TEXT("LifetimeKills")); // The stat to rank by
        //     WriteObject.DisplayFormat = ELeaderboardFormat::Number;
        //     WriteObject.UpdateMethod = ELeaderboardUpdateMethod::KeepBest; // Or ForceUpdate
        //
        //     // Write the new total kills score
        //     LeaderboardInt->WriteLeaderboards(YourPS->GetSessionId(), UserId.ToSharedRef(), WriteObject);
        //     // Need a completion delegate here too.
        // }
    }
}

// Callback function for when stats writing completes
void AYourGameMode::OnStatsWritten(const FOnlineError& Error)
{
    if (Error.bSucceeded)
    {
        UE_LOG(LogTemp, Log, TEXT("Stats written successfully!"));
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("Failed to write stats: %s"), *Error.GetErrorMessage());
    }
}*/