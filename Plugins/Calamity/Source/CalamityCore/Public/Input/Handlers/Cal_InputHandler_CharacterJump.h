#pragma once

#include "CoreMinimal.h"
#include "GameplayTagContainer.h"
#include "NinjaInputHandler.h"
#include "Cal_InputHandler_CharacterJump.generated.h"

UCLASS(meta = (DisplayName = "Calamity: Character Jump"))
class CALAMITYCORE_API UCal_InputHandler_CharacterJump : public UNinjaInputHandler
{
    GENERATED_BODY()

public:
    UCal_InputHandler_CharacterJump();

protected:
    /** Minimum magnitude required for jump to be accepted (for analog inputs). */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Jump")
    float MinimumMagnitudeToJump;

    /** If any of these tags are present, jumping is blocked. */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Jump")
    FGameplayTagContainer BlockJumpTags;

    /** Checks if the character is allowed to jump. */
    UFUNCTION(BlueprintNativeEvent, Category = "Input Handler|Jump")
    bool CanJump(UNinjaInputManagerComponent* Manager, const FInputActionValue& Value) const;


public:
    virtual void HandleTriggeredEvent_Implementation(UNinjaInputManagerComponent* Manager, 
       const FInputActionValue& Value,
       const UInputAction* InputAction,
       float ElapsedTime) const override;
    
    virtual void HandleCompletedEvent_Implementation(UNinjaInputManagerComponent* Manager,
        const FInputActionValue& Value, const UInputAction* InputAction) const override;
};