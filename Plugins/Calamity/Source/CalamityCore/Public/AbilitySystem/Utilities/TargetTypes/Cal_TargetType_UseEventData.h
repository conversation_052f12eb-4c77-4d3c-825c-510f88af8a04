// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Cal_TargetType.h"
#include "Cal_TargetType_UseEventData.generated.h"

/** Trivial target type that pulls the target out of the event data */
UCLASS(NotBlueprintable)
class CALAMITYCORE_API UCal_TargetType_UseEventData : public UCal_TargetType
{
	GENERATED_BODY()
public:
	// Constructor and overrides
	UCal_TargetType_UseEventData() {}

	/** Uses the passed in event data */
	virtual void GetTargets_Implementation(AActor* TargetingActor, FGameplayEventData EventData, TArray<FHitResult>& OutHitResults, TArray<AActor*>& OutActors) const override;
};
