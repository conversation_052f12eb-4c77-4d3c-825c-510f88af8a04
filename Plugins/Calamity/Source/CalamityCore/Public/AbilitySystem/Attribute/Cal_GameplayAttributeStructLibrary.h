// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "AttributeSet.h"
#include "GameplayEffectTypes.h"
#include "Cal_GameplayAttributeStructLibrary.generated.h"

USTRUCT(BlueprintType)
struct FCal_ModifiedAttribute
{
	GENERATED_BODY()

	/** The attribute that has been modified */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="GCS")
	FGameplayAttribute Attribute;

	/** Total magnitude applied to that attribute */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="GCS")
	float TotalMagnitude{0};
};


/**
 * Information about PostGameplayEffectExecute event with related objects cached.
 */
USTRUCT(BlueprintType)
struct FCal_GameplayEffectModCallbackData
{
	GENERATED_BODY()

	/**
	 * The owner AttributeSet from which the event was invoked
	 */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "GGA")
	TObjectPtr<UAttributeSet> AttributeSet = nullptr;

	/**
	 * Evaluated data about this change.
	 */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "GGA")
	FGameplayModifierEvaluatedData EvaluatedData;

	/**
	 * Any modified attributes.
	 */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "GGA")
	TArray<FCal_ModifiedAttribute> ModifiedAttributes;

	/**
	 * Map of set by caller magnitudes
	 */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "GGA")
	TMap<FName, float> SetByCallerNameMagnitudes;

	/**
	 * Map of set by caller magnitudes
	 */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "GGA")
	TMap<FGameplayTag, float> SetByCallerTagMagnitudes;

	/**
	 * The context of The effect spec that the mod came from
	 */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "GGA")
	FGameplayEffectContextHandle ContextHandle;

	/**
	 * The instigator actor within context.
	 */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "GGA")
	TWeakObjectPtr<AActor> InstigatorActor = nullptr;

	/**
	 * The target actor within context.
	 */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "GGA")
	TWeakObjectPtr<AActor> TargetActor = nullptr;

	/**
	 * Target we intend to apply to。
	 */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "GGA")
	TObjectPtr<UAbilitySystemComponent> TargetAsc = nullptr;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "GGA")
	FGameplayTagContainer AggregatedSourceTags = FGameplayTagContainer::EmptyContainer;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "GGA")
	FGameplayTagContainer AggregatedTargetTags = FGameplayTagContainer::EmptyContainer;
};
