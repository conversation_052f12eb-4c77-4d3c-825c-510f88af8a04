// MeleeAttackAbility.h
#pragma once

#include "CoreMinimal.h"
#include "CalamityGameplayAbility.h"
#include "MeleeAttackAbility.generated.h"

UCLASS()
class CALAMITYCORE_API UMeleeAttackAbility : public UCalamityGameplayAbility
{
    GENERATED_BODY()
    
public:
    UMeleeAttackAbility();
    
    // Weapon to use for this attack
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Melee Attack")
    TSubclassOf<AActor> WeaponClass;
    
    // Attack montage to play
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Melee Attack")
    UAnimMontage* AttackMontage;
    
    // Damage to apply
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Melee Attack")
    float BaseDamage;
    
    // Attack range
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Melee Attack")
    float AttackRange;
    
    // Attack angle (in degrees)
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Melee Attack")
    float AttackAngle;
    
    // Whether this is an AOE attack
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Melee Attack")
    bool bIsAreaAttack;
    
    // AOE radius (if applicable)
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Melee Attack", meta = (EditCondition = "bIsAreaAttack"))
    float AreaRadius;
    
    // Override activation
    virtual void ActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, const FGameplayEventData* TriggerEventData) override;
    
protected:
    // Handle ability task delegates
    UFUNCTION()
    void OnMontageCompleted();
    
    UFUNCTION()
    void OnMontageCancelled();
    
    UFUNCTION()
    void OnMontageBlendOut();
    
    // Apply damage to hit targets
    UFUNCTION(BlueprintCallable, Category = "Melee Attack")
    void ApplyDamage();
};