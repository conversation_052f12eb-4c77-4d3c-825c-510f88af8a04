// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameplayEffectTypes.h"
#include "GameplayTagContainer.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "Cal_GameplayCueFunctionLibrary.generated.h"

/**
 * 
 */
UCLASS()
class CALAMITYCORE_API UCal_GameplayCueFunctionLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	UFUNCTION(BlueprintCallable, Category = "Cal|GameplayCue", Meta = (DefaultToSelf="Actor", AutoCreateRefTerm = "GameplayCueParameters", GameplayTagFilter = "GameplayCue"))
	static void ExecuteGameplayCueLocal(AActor* Actor, const FGameplayTag GameplayCueTag, const FGameplayCueParameters& GameplayCueParameters);

	UFUNCTION(BlueprintCallable, Category = "Cal|GameplayCue", Meta = (DefaultToSelf="Actor", AutoCreateRefTerm = "GameplayCueParameters", GameplayTagFilter = "GameplayCue"))
	static void AddGameplayCueLocal(AActor* Actor, const FGameplayTag GameplayCueTag, const FGameplayCueParameters& GameplayCueParameters);

	UFUNCTION(BlueprintCallable, Category = "Cal|GameplayCue", Meta = (DefaultToSelf="Actor", AutoCreateRefTerm = "GameplayCueParameters", GameplayTagFilter = "GameplayCue"))
	static void RemoveGameplayCueLocal(AActor* Actor, const FGameplayTag GameplayCueTag, const FGameplayCueParameters& GameplayCueParameters);
};
