// Copyright (C) Your Company. All Rights Reserved.
#pragma once

#include "CoreMinimal.h"
#include "UObject/Interface.h"
#include "Animation/AnimMontage.h"
#include "ICalAnimationProvider.generated.h"

UENUM(BlueprintType)
enum class EAnimationState : uint8
{
    Default     UMETA(DisplayName = "Default"),
    Unarmed     UMETA(DisplayName = "Unarmed"),
    Rifle       UMETA(DisplayName = "Rifle"),
    Pistol      UMETA(DisplayName = "Pistol"),
    Shotgun     UMETA(DisplayName = "Shotgun"),
    Melee       UMETA(DisplayName = "Melee"),
    DualWield   UMETA(DisplayName = "Dual Wield"),
    TwoHanded   UMETA(DisplayName = "Two Handed"),
    Shield      UMETA(DisplayName = "Shield"),
    Staff       UMETA(DisplayName = "Staff"),
    Bow         UMETA(DisplayName = "Bow"),
    // Add other states as needed
};

UINTERFACE(MinimalAPI, Blueprintable)
class UCalAnimationProvider : public UInterface
{
    GENERATED_BODY()
};

class CALAMITYCORE_API ICalAnimationProvider
{
    GENERATED_BODY()

public:
    UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Animation|BPI")
    void SetAnimationState(EAnimationState NewState);

    UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Animation|BPI")
    float PlayMontage(UAnimMontage* Montage, float PlayRate, float StartTime);

    UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Animation|BPI")
    void SetActionMode(bool bInActionMode);

    UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Animation|BPI")
    EAnimationState GetAnimationState();

    UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Animation|BPI")
    bool IsInActionMode();

    UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Animation|BPI")
    void StopMontage(float BlendOutTime);

    UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Animation|BPI")
    bool IsPlayingMontage();
};