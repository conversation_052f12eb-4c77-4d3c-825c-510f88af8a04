#pragma once
#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "GameplayTagContainer.h"
#include "Interfaces/ICalamityEncounterInterface.h"
#include "CalamityEncounterComponent.generated.h"

// Forward declarations
class UCalamityEncounterManagerSubsystem;
class UAbilitySystemComponent; 

// Delegate signatures for broadcasting changes
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAggroChanged, float, NewAggro);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnThreatLevelChanged, EThreatLevel, NewLevel, EThreatLevel, OldLevel);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPerkAdded, const FGameplayTag&, PerkTag);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPerkRemoved, const FGameplayTag&, PerkTag);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnEncounterIntensityChanged, float, Intensity, AActor*, OpponentActor);

UCLASS(ClassGroup=(Calamity), meta=(BlueprintSpawnableComponent))
class CALAMITYCORE_API UCalamityEncounterComponent : public UActorComponent, public ICalamityEncounterInterface
{
    GENERATED_BODY()

public:
    UCalamityEncounterComponent();

public:
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Calamity")
    FGameplayTagContainer CalamityTags;

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	UPROPERTY(VisibleInstanceOnly, Category = "Calamity|Runtime")
	bool bShouldTick;

	// --- Core State ---

	/** Current Aggro score (0-100) */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Encounter State", ReplicatedUsing=OnRep_CurrentAggro) // Consider replication if needed
	float CurrentAggro = 0.0f;

	/** Current Threat Level */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Encounter State", ReplicatedUsing=OnRep_CurrentThreatLevel) // Consider replication
	EThreatLevel CurrentThreatLevel = EThreatLevel::None;

	/** Container for active Perks */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Encounter State", ReplicatedUsing=OnRep_ActivePerks) // Consider replication
	FGameplayTagContainer ActivePerks;

	/** Time spent above the threshold for the next Threat Level */
	UPROPERTY() // Not exposed, internal tracking
	float TimeAboveThreatThreshold = 0.0f;

	/** Tracks current suppression value/status */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Encounter State") // Add replication if needed
	float CurrentSuppression = 0.0f;

	/** Pointer to the current rival, if any */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Encounter State") // Add replication if needed
	TWeakObjectPtr<AActor> CurrentRival;

	// --- Configuration ---

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Encounter Config|Threat")
	float AggroDecayRate = 5.0f; // Points per second when not gaining aggro

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Encounter Config|Threat")
	TMap<EThreatLevel, float> ThreatLevelAggroThresholds; // Aggro needed to reach this level

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Encounter Config|Threat")
	TMap<EThreatLevel, float> ThreatLevelSustainDuration; // Time needed above threshold to promote

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Encounter Config|Suppression")
	float MaxSuppression = 100.0f;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Encounter Config|Suppression")
	float SuppressionDecayRate = 10.0f;
public:	
    // --- Internal Logic ---

	/** Calculates the new Threat Level based on current Aggro and sustain time */
	UFUNCTION()
	void UpdateThreatLevel(float DeltaTime);

	/** Applies Aggro decay */
	UFUNCTION()
	void DecayAggro(float DeltaTime);

	/** Applies Suppression decay */
	UFUNCTION()
	void DecaySuppression(float DeltaTime);

	/** Handles replication callbacks */
	UFUNCTION()
	void OnRep_CurrentAggro();
	UFUNCTION()
	void OnRep_CurrentThreatLevel();
	UFUNCTION()
	void OnRep_ActivePerks();

	// --- Interface Implementation ---

	virtual void AddAggro_Implementation(const FAggroEventData& AggroData) override;
	virtual float GetCurrentAggro_Implementation() const override;
	virtual EThreatLevel GetCurrentThreatLevel_Implementation() const override;
	virtual void AddPerk_Implementation(const FGameplayTag& PerkTag) override;
	virtual void RemovePerk_Implementation(const FGameplayTag& PerkTag) override;
	virtual bool HasPerk_Implementation(const FGameplayTag& PerkTag) const override;
	virtual FGameplayTagContainer GetActivePerks_Implementation() const override;
	virtual void ApplyEncounterEffect_Implementation(const FGameplayEffectSpecHandle& EffectSpecHandle) override;
	virtual void NotifyEncounterEvent_Implementation(const FGameplayTag& EventTag, AActor* Instigator) override;
	virtual void NotifySuppression_Implementation(AActor* Suppressor, float SuppressionAmount) override;
	virtual void UpdateRivalStatus_Implementation(AActor* RivalActor, bool bIsNowRival) override;
	virtual FGameplayTagContainer GetCalamityTags_Implementation() const override { return CalamityTags; }
	virtual void OnEncounterStarted_Implementation(bool bIsOneVsOne, const TArray<AActor*>& Participants) override;
	virtual void OnEncounterEnded_Implementation() override;

	// --- GAS Interaction (Example) ---
	// Weak pointer to the owner's Ability System Component, cached in BeginPlay
	UPROPERTY()
	TWeakObjectPtr<UAbilitySystemComponent> OwnerAbilitySystemComponent;

	// --- Subsystem Reference ---
	UPROPERTY()
	TWeakObjectPtr<UCalamityEncounterManagerSubsystem> EncounterManagerSubsystem;
	
	// --- Delegates ---
	UPROPERTY(BlueprintAssignable, Category = "Encounter Events")
	FOnAggroChanged OnAggroChanged;

	UPROPERTY(BlueprintAssignable, Category = "Encounter Events")
	FOnThreatLevelChanged OnThreatLevelChanged;

	UPROPERTY(BlueprintAssignable, Category = "Encounter Events")
	FOnPerkAdded OnPerkAdded;

	UPROPERTY(BlueprintAssignable, Category = "Encounter Events")
	FOnPerkRemoved OnPerkRemoved;

	UPROPERTY(BlueprintAssignable, Category = "Encounter Events")
	FOnEncounterIntensityChanged OnEncounterIntensityChanged;

	// Helper function to get owner ASC
	UFUNCTION(BlueprintPure, Category = "Encounter|GAS")
	UAbilitySystemComponent* GetOwnerAbilitySystemComponent() const;

	// Debug

	/** Draws debug info for Visualizing encounters */
	UFUNCTION(BlueprintCallable, Category = "Debug")
	void DebugDrawEncounterInfo(float DeltaTime);
	UFUNCTION(BlueprintCallable, Category = "Debug")
	void ToggleDebugDraw();
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
	bool bDrawDebug = false;

	// Required for replication
	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;
};
