// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameplayTagContainer.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "GameFramework/Systems/CalamityEncounterTypes.h"
#include "CalamityEncounterManagerSubsystem.generated.h"

// Forward declarations
class UCalamityEncounterComponent;
class APlayerState; // Needed for tracking player data like Rivals

/** Struct to hold rivalry data (MP)*/
USTRUCT(BlueprintType)
struct FRivalData
{
	GENERATED_BODY()
	
	UPROPERTY(BlueprintReadWrite)
		FString RivalID;
	UPROPERTY(BlueprintReadWrite)
		bool bIsPlayerRival = false; // True for multiplayer rivals
	UPROPERTY(BlueprintReadWrite)
		int32 TimesEncountered = 0;
	UPROPERTY(BlueprintReadWrite)
		int32 KillsAgainstRival = 0; // Player killed rival
	UPROPERTY(BlueprintReadWrite)
		int32 PlayerWins = 0;
	UPROPERTY(BlueprintReadWrite)
		int32 RivalWins = 0;
	UPROPERTY(BlueprintReadWrite)
		int32 DeathsByRival = 0;  // Rival killed player
	UPROPERTY(BlueprintReadWrite)
		FGameplayTagContainer LastEncounterTags; // Roles in last encounter
	UPROPERTY(BlueprintReadWrite)
		FGameplayTagContainer LastEncounterOutcome;
	UPROPERTY(BlueprintReadWrite)
		ERivalryIntensity RivalryIntensity = ERivalryIntensity::None;
	UPROPERTY(BlueprintReadWrite)
		float RivalryScore = 0.0f;  // Composite score for rivalry strength
	UPROPERTY(BlueprintReadWrite)
		FDateTime LastInteractionTime;
	UPROPERTY(BlueprintReadWrite)
		TMap<FGameplayTag, FObjectiveRivalryData> ObjectiveRivalries;
	UPROPERTY(BlueprintReadWrite)
		FProximityEncounterData ProximityData;
};

/** Struct to hold nemesis data (SM)*/
USTRUCT(BlueprintType)
struct FNemesisData
{
	GENERATED_BODY()
	UPROPERTY(BlueprintReadWrite)
		FString NemesisID;
	UPROPERTY(BlueprintReadWrite)
		int32 Encounters = 0;
	UPROPERTY(BlueprintReadWrite)
		int32 PlayerWins = 0;
	UPROPERTY(BlueprintReadWrite)
		int32 NemesisWins = 0;
	UPROPERTY(BlueprintReadWrite)
		FGameplayTagContainer LastEncounterTags; // Roles in last encounter
	UPROPERTY(BlueprintReadWrite)
		FString LastEncounterOutcome;
};

// Wrapper struct to allow TMap inside TMap with UPROPERTY support
USTRUCT(BlueprintType)
struct FRivalMapWrapper
{
	GENERATED_BODY()

	// This map holds the actual RivalData keyed by the Victim's Unique ID
	UPROPERTY()
	TMap<uint32, FRivalData> VictimMap;
};

/** Spatial hashing for efficient proximity queries */
USTRUCT()
struct FSpatialHashCell
{
	GENERATED_BODY()
	
	TSet<uint32> PlayerIDs;
	FVector CellCenter;
	float CellSize = 5000.0f;  // 50m cells
};

/** Event batch for frame aggregation */
USTRUCT()
struct FBatchedAggroEvent
{
	GENERATED_BODY()
	
	TArray<FAggroEventData> Events;
	float TotalAggro = 0.0f;
	FGameplayTagContainer CombinedTags;
};

/**
 * Manages encounters between players and tracks nemesis/rival history
 */
UCLASS(Config = CalamityEncounterManagerSubsystem)
class CALAMITYCORE_API UCalamityEncounterManagerSubsystem : public UGameInstanceSubsystem
{
	GENERATED_BODY()

private:
	FDelegateHandle TickDelegateHandle;
	bool Tick(float DeltaTime);	

public:
	virtual void Initialize(FSubsystemCollectionBase& Collection) override;
	virtual void Deinitialize() override;
	// Tick support via FTSTicker

	/** Register a component with the subsystem */
	void RegisterEncounterComponent(UCalamityEncounterComponent* Component);

	/** Unregister a component */
	void UnregisterEncounterComponent(UCalamityEncounterComponent* Component);

	/** Processes a globally relevant Aggro event (e.g., from damage system, objective capture) */
	UFUNCTION(BlueprintCallable, Category = "Encounter Manager")
	void ProcessGlobalAggroEvent(const FAggroEventData& AggroData);

	// Batch processing (called at end of frame)
	void ProcessBatchedAggroEvents();

	/** Call this when a player gets a kill to update Rivalries */
	UFUNCTION(BlueprintCallable, Category = "Encounter Manager")
	void NotifyPlayerKill(APlayerState* KillerPlayerState, APlayerState* VictimPlayerState);

	// Proximity tracking
	UFUNCTION(BlueprintCallable, Category = "Encounter Manager")
	void UpdatePlayerProximity(APlayerState* PlayerA, APlayerState* PlayerB, float Distance, bool bInCombat);

	// Objective rivalry tracking
	UFUNCTION(BlueprintCallable, Category = "Encounter Manager")
	void NotifyObjectiveContest(APlayerState* PlayerA, APlayerState* PlayerB, const FGameplayTag& ObjectiveTag, APlayerState* Winner);

	// Enhanced rivalry queries
	UFUNCTION(BlueprintCallable, Category = "Encounter Manager")
	ERivalryIntensity GetRivalryIntensity(APlayerState* PlayerA, APlayerState* PlayerB) const;
	
	UFUNCTION(BlueprintCallable, Category = "Encounter Manager")
	TArray<FRivalData> GetTopRivals(APlayerState* ForPlayer, int32 MaxCount = 5) const;

	// Active encounter management
	UFUNCTION(BlueprintCallable, Category = "Encounter Manager")
	void StartEncounter(const TArray<APlayerState*>& Participants, bool bNearObjective, const FGameplayTag& ObjectiveTag);
	
	UFUNCTION(BlueprintCallable, Category = "Encounter Manager")
	void UpdateEncounterState(int32 EncounterID, EEncounterState NewState);
	
	UFUNCTION(BlueprintCallable, Category = "Encounter Manager")
	void EndEncounter(int32 EncounterID);

	// Scoring and feedback
	UFUNCTION(BlueprintCallable, Category = "Encounter Manager")
	void UpdateEncounterScore(int32 EncounterID, APlayerState* Player, const FEncounterScore& ScoreUpdate);
	
	UFUNCTION(BlueprintCallable, Category = "Encounter Manager")
	FEncounterScore GetEncounterScore(int32 EncounterID, APlayerState* Player) const;

	// Visual/Audio feedback helpers
	UFUNCTION(BlueprintCallable, Category = "Encounter Manager")
	float GetEncounterIntensity(int32 EncounterID) const;
	
	UFUNCTION(BlueprintCallable, Category = "Encounter Manager")
	bool ShouldTriggerRivalryVO(APlayerState* PlayerA, APlayerState* PlayerB) const;

	// Team-based encounter support
	UFUNCTION(BlueprintCallable, Category = "Encounter Manager")
	void NotifyTeamEncounter(const TArray<APlayerState*>& TeamA, const TArray<APlayerState*>& TeamB);
	
	UFUNCTION(BlueprintCallable, Category = "Encounter Manager")
	float GetTeamThreatLevel(int32 TeamID) const;

	// Debug visualization
	UFUNCTION(BlueprintCallable, Category = "Encounter Manager|Debug", meta = (CallInEditor = "true"))
	void ToggleDebugVisualization();
	
	UFUNCTION(BlueprintCallable, Category = "Encounter Manager|Debug")
	void DrawDebugEncounterInfo(float DeltaTime);

	void UpdateRivalData(FString RivalID, bool bIsPlayerRival, bool bPlayerWon, FGameplayTagContainer EncounterTags);

	FRivalData GetRivalData(FString RivalID) const;

	// Add functions for:
	// - Calculating visibility/focus factor between players/teams
	// - Evaluating encounter states (e.g., is a 1v1 happening near objective X?)
	// - Triggering global presentation effects (e.g., announcer lines)
	// - Querying overall team threat levels

	FNemesisData GetNemesisData(FString NemesisID) const;
	
	UFUNCTION(BlueprintCallable)
	void UpdateNemesisRecord(FString NemesisID, bool bPlayerWon);

	void UpdateNemesisData(FString NemesisID, bool bPlayerWon);

	UFUNCTION(BlueprintCallable)
	FNemesisData GetNemesisRecord(FString NemesisID) const;

	UPROPERTY(BlueprintReadOnly)
	TArray<FNemesisData> NemesisHistory;

	UFUNCTION(BlueprintCallable)
	void UpdateRivalRecord(FString RivalID, bool bIsPlayerRival, bool bPlayerWon, FGameplayTagContainer EncounterTags);

	UFUNCTION(BlueprintCallable)
	FRivalData GetRivalRecord(FString RivalID) const;

	UPROPERTY(BlueprintReadOnly)
	TArray<FRivalData> RivalHistory;

protected:

	/** List of currently active encounter components */
	UPROPERTY()
	TArray<TWeakObjectPtr<UCalamityEncounterComponent>> RegisteredComponents;
	
	/**
	 * Map storing rivalry data between players.
	 * Key = Killer PlayerState Unique ID.
	 * Value = Wrapper struct containing another map where Key = Victim PlayerState Unique ID, Value = FRivalData.
	 */
	UPROPERTY()
	TMap<uint32, FRivalMapWrapper> RivalryMap;

	// Spatial hashing for proximity
	TMap<FIntVector, FSpatialHashCell> SpatialHash;
	float SpatialHashCellSize = 5000.0f;  // 50m cells

	// Active encounters
	UPROPERTY()
	TMap<int32, FActiveEncounter> ActiveEncounters;
	int32 NextEncounterID = 1;

	// Batched events for frame aggregation
	TMap<uint32, FBatchedAggroEvent> BatchedAggroEvents;

	// Configuration for Rivalry system
	UPROPERTY(Config) // Example: Make configurable via INI
	int32 RivalryKillThreshold = 5;
	
	UPROPERTY(Config)
	int32 RivalryStreakThreshold = 2;

	UPROPERTY(Config)
	float RivalryDecayTime = 300.0f;  // 5 minutes
	
	UPROPERTY(Config)
	float ProximityThreshold = 3000.0f;  // 30m
	
	UPROPERTY(Config)
	float CombatProximityMultiplier = 2.0f;
	
	UPROPERTY(Config)
	bool bEnableDebugVisualization = false;

private:
	
	// Helper function to get PlayerState Unique ID safely
	uint32 GetPlayerUniqueId(APlayerState* PlayerState) const;
	FIntVector GetSpatialHashKey(const FVector& Location) const;
	void UpdateSpatialHash(uint32 PlayerID, const FVector& Location);
	TArray<uint32> GetNearbyPlayers(const FVector& Location, float Radius) const;

	/** Updates the Rival status between two players based on kill counts */
	void UpdateRivalryBetweenPlayers(APlayerState* PlayerA, APlayerState* PlayerB);

	/** Checks if two players meet the criteria to be Rivals */
	bool CheckRivalryCriteria(const FRivalData& Data) const;

	void UpdateRivalryIntensity(FRivalData& RivalData);
	void DecayRivalries(float DeltaTime);
	float CalculateRivalryScore(const FRivalData& RivalData) const;

	void ProcessProximityEncounters(float DeltaTime);
	void EvaluateEncounterStates(float DeltaTime);
	
	// Generate unique IDs for Rivals/Nemesis if needed (consider if these should be deterministic)
	static FString GenerateRivalID(const bool bIsPlayerRival) { return bIsPlayerRival ? FString::Printf(TEXT("PLR_%d"), FMath::RandRange(1000, 9999)) : FString::Printf(TEXT("NEM_%d"), FMath::RandRange(1000, 9999)); }
	static FString GenerateNemesisID() { return FString::Printf(TEXT("NEM_%d"), FMath::RandRange(1000, 9999)); }

};

