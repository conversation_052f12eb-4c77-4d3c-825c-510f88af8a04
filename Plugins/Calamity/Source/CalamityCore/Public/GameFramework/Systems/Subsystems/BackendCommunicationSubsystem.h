// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "BackendCommunicationSubsystem.generated.h"

/**
 * 
 */
UCLASS()
class CALAMITYCORE_API UBackendCommunicationSubsystem : public UGameInstanceSubsystem
{
	GENERATED_BODY()

	/*// HTTP requests to your backend
	UFUNCTION(BlueprintCallable)
	void SendPlayerStats(const FPlayerStatsData& Stats);
    
	UFUNCTION(BlueprintCallable)
	void RequestPlayerRating(const FString& PlayerId);
    
	// WebSocket connection for real-time updates
	void ConnectToMatchmakingService();*/
    
private:
	/*TSharedPtr<class IHttpRequest> CreateHttpRequest();
	TSharedPtr<class IWebSocket> MatchmakingSocket;*/
};
