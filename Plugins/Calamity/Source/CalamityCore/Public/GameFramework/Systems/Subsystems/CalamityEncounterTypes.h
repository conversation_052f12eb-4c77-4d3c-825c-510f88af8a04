#pragma once

#include "CoreMinimal.h"
#include "GameplayTagContainer.h" // Include for Gameplay Tags
#include "CalamityEncounterTypes.generated.h" // Use .generated.h for UENUMs/USTRUCTs

// Forward declarations
class AActor;
class UCalamityEncounterComponent;

// Enum defining the different Threat Levels
UENUM(BlueprintType)
enum class EThreatLevel : uint8
{
	None		UMETA(DisplayName = "None"),		// Default state, out of combat/no threat
	Low			UMETA(DisplayName = "Low"),			// Basic engagement threshold
	Medium		UMETA(DisplayName = "Medium"),		// Sustained engagement, minor impact
	High		UMETA(DisplayName = "High"),		// Significant impact/performance
	Extreme		UMETA(DisplayName = "Extreme"),		// Peak performance, clutch moments
	Destroyer	UMETA(DisplayName = "Destroyer")	// Linked to Godlike Ult / Max Threat state
};

// Example struct to pass Aggro event data
// Could be expanded significantly
USTRUCT(BlueprintType)
struct FAggroEventData
{
	GENERATED_BODY()

	// The actor that caused this aggro event (e.g., damage dealer, healer)
	UPROPERTY(BlueprintReadWrite, Category = "Encounter")
	TWeakObjectPtr<AActor> InstigatorActor;

	// The actor that is the target of this aggro event (e.g., damage receiver)
	UPROPERTY(BlueprintReadWrite, Category = "Encounter")
	TWeakObjectPtr<AActor> TargetActor;

	// The base amount of Aggro generated by this event
	UPROPERTY(BlueprintReadWrite, Category = "Encounter")
	float BaseAggroAmount = 0.0f;

	// Tags associated with this event (e.g., Ability.Tag, DamageType.Fire)
	UPROPERTY(BlueprintReadWrite, Category = "Encounter")
	FGameplayTagContainer EventTags;

	// World location where the event occurred
	UPROPERTY(BlueprintReadWrite, Category = "Encounter")
	FVector WorldLocation = FVector::ZeroVector;

	// Add other relevant data: Damage amount, Healing amount, Ability used, etc.
};

// You might also define structs for Rivalry Data, Perk Data, Effect Data here
