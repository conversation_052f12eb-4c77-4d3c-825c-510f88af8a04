// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Interfaces/OnlineSessionInterface.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "MatchmakingSubsystem.generated.h"

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnMatchFoundDelegate, const FString&, MatchID);

 /**
 * Subsystem that manages matchmaking and player matching
 */
UCLASS()
class CALAMITYCORE_API UMatchmakingSubsystem : public UGameInstanceSubsystem
{
	GENERATED_BODY()

public:
	virtual void Initialize(FSubsystemCollectionBase& Collection) override;
	virtual void Deinitialize() override;
	
	// Queue management
	UFUNCTION(BlueprintCallable)
	void JoinMatchmakingQueue(const FString& GameMode);
    
	UFUNCTION(BlueprintCallable)
	void LeaveMatchmakingQueue();
    
	// Match found callbacks
	UPROPERTY(BlueprintAssignable)
	FOnMatchFoundDelegate OnMatchFound;

private:

// EOS/Steam session interfaces
	TSharedPtr<FOnlineSessionSearch> SessionSearch;
	TWeakPtr<IOnlineSession> SessionInterface;
};
