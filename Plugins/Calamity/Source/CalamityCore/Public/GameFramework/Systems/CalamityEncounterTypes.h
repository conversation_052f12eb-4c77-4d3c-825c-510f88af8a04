#pragma once

#include "CoreMinimal.h"
#include "GameplayTagContainer.h" // Include for Gameplay Tags
#include "CalamityEncounterTypes.generated.h" // Use .generated.h for UENUMs/USTRUCTs

// Forward declarations
class AActor;
class UCalamityEncounterComponent;

/** Enum defining the different Threat Levels */
UENUM(BlueprintType)
enum class EThreatLevel : uint8
{
	None		UMETA(DisplayName = "None"),		// Default state, out of combat/no threat
	Low			UMETA(DisplayName = "Low"),			// Basic engagement threshold
	Medium		UMETA(DisplayName = "Medium"),		// Sustained engagement, minor impact
	High		UMETA(DisplayName = "High"),		// Significant impact/performance
	Extreme		UMETA(DisplayName = "Extreme"),		// Peak performance, clutch moments
	Destroyer	UMETA(DisplayName = "Destroyer")	// Linked to Godlike Ult / Max Threat state
};

/** Enum defining the different encounter states */
UENUM(BlueprintType)
enum class EEncounterState : uint8
{
	None		UMETA(DisplayName = "None"),				// No encounter present
	Engaging	UMETA(DisplayName = "Engaging/Starting"),	// Encounter is starting, but not yet active
	Active		UMETA(DisplayName = "Active"),				// Encounter is active and in progress
	Resolving	UMETA(DisplayName = "Resolving"),			// Encounter is resolving, but not yet complete
	Climax		UMETA(DisplayName = "Climax"),				// Encounter is at its climax, often linked to ultimates
	Resolution	UMETA(DisplayName = "Resolution"),			// Encounter is resolving. Not yet completed. Used for cleaning up
	Complete	UMETA(DisplayName = "Complete")				// Encounter is complete and cleaned up
};

// New enum for rivalry intensity
UENUM(BlueprintType)
enum class ERivalryIntensity : uint8
{
	None		UMETA(DisplayName = "None"),
	Budding		UMETA(DisplayName = "Budding"),			// Early stages
	Active		UMETA(DisplayName = "Active"),			// Regular encounters
	Heated		UMETA(DisplayName = "Heated"),			// Frequent conflicts
	Nemesis		UMETA(DisplayName = "Nemesis")			// Arch-enemy status
};

/** Enum defining the different encounter types */
UENUM(BlueprintType)
enum class EEncounterType : uint8
{
	Story		UMETA(DisplayName = "Story"),
	MultiPlayer	UMETA(DisplayName = "MultiPlayer")
};


/** Struct to pass Aggro event data */
USTRUCT(BlueprintType)
struct FAggroEventData
{
	GENERATED_BODY()

	// The actor that caused this aggro event (e.g., damage dealer, healer)
	UPROPERTY(BlueprintReadWrite, Category = "Encounter")
	TWeakObjectPtr<AActor> InstigatorActor;

	// The actor that is the target of this aggro event (e.g., damage receiver)
	UPROPERTY(BlueprintReadWrite, Category = "Encounter")
	TWeakObjectPtr<AActor> TargetActor;

	// The base amount of Aggro generated by this event
	UPROPERTY(BlueprintReadWrite, Category = "Encounter")
	float BaseAggroAmount = 0.0f;

	// Tags associated with this event (e.g., Ability.Tag, DamageType.Fire)
	UPROPERTY(BlueprintReadWrite, Category = "Encounter")
	FGameplayTagContainer EventTags;

	// World location where the event occurred
	UPROPERTY(BlueprintReadWrite, Category = "Encounter")
	FVector WorldLocation = FVector::ZeroVector;

	UPROPERTY(BlueprintReadWrite, Category = "Encounter")
	bool bIsObjectiveRelated = false;

	UPROPERTY(BlueprintReadWrite, Category = "Encounter")
	float DamageAmount = 0.0f;

	UPROPERTY(BlueprintReadWrite, Category = "Encounter")
	bool bIsHealing = false;

	// Add other relevant data: Damage amount, Healing amount, Ability used, etc.
};

/** Proximity tracking data */
USTRUCT(BlueprintType)
struct FProximityEncounterData
{
	GENERATED_BODY()

	UPROPERTY()
	uint32 OtherPlayerID = 0;

	UPROPERTY()
	float TimeInProximity = 0.0f;

	UPROPERTY()
	float CombatTimeInProximity = 0.0f;

	UPROPERTY()
	int32 EngagementCount = 0;

	UPROPERTY()
	FDateTime LastEngagementTime;

	UPROPERTY()
	float AverageThreatLevelDuringEngagements = 0.0f;
};

/** Struct to hold rivalry data at an objective */
USTRUCT(BlueprintType)
struct FObjectiveRivalryData
{
	GENERATED_BODY()

	UPROPERTY()
	FGameplayTag ObjectiveTag;

	UPROPERTY()
	int32 ContestCount = 0;			// Times both players competed for this objective

	UPROPERTY()
	int32 PlayerWins = 0;

	UPROPERTY()
	int32 RivalWins = 0;

	UPROPERTY()
	float TotalContestDuration = 0.0f;
};

/** Encounter scoring for quality evaluation */
USTRUCT(BlueprintType)
struct FEncounterScore
{
	GENERATED_BODY()
    
	UPROPERTY()
	float DamageDealt = 0.0f;
    
	UPROPERTY()
	float DamageTaken = 0.0f;

	UPROPERTY(BlueprintReadOnly)
	float HealingDone = 0.0f;

	UPROPERTY(BlueprintReadOnly)
	float HealingReceived = 0.0f;
    
	UPROPERTY()
	float ObjectiveContribution = 0.0f;
    
	UPROPERTY()
	float TeamplayScore = 0.0f;
    
	UPROPERTY()
	int32 KillStreak = 0;

	UPROPERTY(BlueprintReadOnly)
	float EncounterDuration = 0.0f;

	UPROPERTY(BlueprintReadOnly)
	float SkillRating = 0.0f;			// Based on accuracy, ability usage, etc.

	float CalculateTotalScore() const
	{
		return (DamageDealt * 0.3f) + (DamageTaken * 0.1f) + (HealingDone * 0.2f) + 
			   (ObjectiveContribution * 0.25f) + (TeamplayScore * 0.15f) + (KillStreak * 10.0f);
	}
};

/** Struct to hold active encounter tracking data */
USTRUCT(BlueprintType)
struct FActiveEncounter
{
	GENERATED_BODY()

	UPROPERTY()
	TArray<uint32> ParticipantIDs;

	UPROPERTY()
	FDateTime StartTime;

	UPROPERTY()
	EEncounterState CurrentState = EEncounterState::None;

	UPROPERTY()
	TMap<uint32, FEncounterScore> ParticipantScores;

	UPROPERTY()
	FVector EncounterCenter = FVector::ZeroVector;

	UPROPERTY()
	float EncounterRadius = 2000.0f;

	UPROPERTY()
	bool bIsNearObjective = false;

	UPROPERTY()
	FGameplayTag NearbyObjectiveTag;
};

// You might also define structs for Rivalry Data, Perk Data, Effect Data here
