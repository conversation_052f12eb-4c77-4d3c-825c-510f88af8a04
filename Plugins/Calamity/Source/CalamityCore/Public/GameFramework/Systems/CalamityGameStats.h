// In a suitable header file (e.g., CalamityGameStats.h)
#pragma once

#include "CoreMinimal.h"
#include "Engine/DataTable.h" // If you want to use Data Tables for base hero stats, etc.
#include "CalamityGameStats.generated.h"

/*
 * Enum for Match Outcome
 */
UENUM(BlueprintType)
enum class EMatchOutcome : uint8
{
    Win,
    Loss,
    Tie,
    PlayerDisconnected,
    NetworkFailure
};

/*
 * Helper structs for kills by weapons.
 * Since TMap can't be replicated. 
 */
USTRUCT(BlueprintType)
struct FWeaponKillCount
{
    GENERATED_BODY()
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    FName WeaponName;
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    int32 KillCount = 0;
};

/*
 * Helper structs for kills by abilities.
 * Since TMap can't be replicated. 
 */
USTRUCT(BlueprintType)
struct FAbilityKillCount
{
    GENERATED_BODY()
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    FName AbilityName;
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    int32 KillCount = 0;
};

// Stats relevant for a single match
USTRUCT(BlueprintType)
struct FPlayerMatchStats
{
    GENERATED_BODY()

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    int32 Kills = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    int32 Deaths = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    int32 Assists = 0; // Example

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    float DamageDealt = 0.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    float DamageTaken = 0.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    float HealingDone = 0.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    float HealingReceived = 0.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats|Time")
    float TimePlayedInMatch = 0.0f; // In seconds
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats|Time")
    float ObjectiveTime = 0.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    int32 ObjectivesCompleted = 0;
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    int32 ObjectiveInteractions = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    int32 Score = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    int32 Revives = 0; // For team-based games
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    int32 AbilitiesUsed = 0;
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    TArray<FWeaponKillCount> KillsByWeapon;
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    TArray<FAbilityKillCount> KillsByAbility;
    // Add other relevant per-match stats...
};

// Stats that persist across matches and sessions
USTRUCT(BlueprintType)
struct FPlayerPersistentStats
{
    GENERATED_BODY()

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    int32 LifetimeKills = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    int32 LifetimeDeaths = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    float LifetimeDamageDealt = 0.0f;
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    float LifetimeDamageTaken = 0.0f;
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    float LifetimeHealingDone = 0.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats|Match")
    int32 MatchesPlayed = 0;
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats|}")
    int32 MatchesWon = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    int32 MatchesLost = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    int32 MatchesTied = 0;

    // You might store MMR, Rank Points, etc., here too
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    int32 RankPoints = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    int32 SkillRating = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    int32 MatchMakingRating = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats|Time")
    float LifetimeObjectiveTime = 0.0f;
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats|Time")
    float LifetimeTimePlayed = 0.0f;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    int32 CurrentWinStreak = 0;
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    int32 LongestWinStreak = 0;
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    int32 CurrentLossStreak = 0;
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
    int32 LongestLossStreak = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Progression")
    int32 CurrentExperiencePoints = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Progression")
    int32 ExperiencePointsToNextLevel = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Progression")
    int32 LifetimeExperiencePoints = 0;
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Progression")
    int32 PlayerLevel = 1;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Currency")
    int32 Gold = 0;
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Currency")
    int32 PremiumCurrency = 0;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Player Info")
    FDateTime LastPlayedTimestamp;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Player Info")
    TArray<FName> UnlockedCosmetics;
    
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Player Info")
    TArray<FName> CompletedAchievements;
    // Add other relevant lifetime stats...

    // Function to merge match stats into persistent stats
    void MergeMatchStats(const FPlayerMatchStats& MatchStats, EMatchOutcome MatchOutcome)
    {
        LifetimeKills += MatchStats.Kills;
        LifetimeDeaths += MatchStats.Deaths;
        LifetimeDamageDealt += MatchStats.DamageDealt;
        LifetimeDamageTaken += MatchStats.DamageTaken;
        LifetimeHealingDone += MatchStats.HealingDone;
        LifetimeObjectiveTime += MatchStats.ObjectiveTime;
        LifetimeTimePlayed += MatchStats.TimePlayedInMatch;
        // Add other stats...

        MatchesPlayed++;
        switch (MatchOutcome)
        {
            case EMatchOutcome::Win:
            MatchesWon++;
                CurrentWinStreak++;
                CurrentLossStreak = 0;
                if (CurrentWinStreak > LongestWinStreak)
        {
                    LongestWinStreak = CurrentWinStreak;
                }
                break;
            case EMatchOutcome::Loss:
            MatchesLost++;
                CurrentLossStreak++;
                CurrentWinStreak = 0;
                if (CurrentLossStreak > LongestLossStreak)
                {
                    LongestLossStreak = CurrentLossStreak;
                }
                break;
            case EMatchOutcome::Tie:
                MatchesTied++;
                CurrentWinStreak = 0;
                CurrentLossStreak = 0;
                break;
            default:
                // Handle disconnects, network failures, etc. if needed
                CurrentWinStreak = 0;
                CurrentLossStreak = 0;
                break;
        }
    }
};