// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/GameMode.h"
#include "MatchmakingGameMode.generated.h"

/**
 * AMatchmakingGameMode runs on the game server and handles match logic
 */
UCLASS()
class CALAMITYCORE_API AMatchmakingGameMode : public AGameMode
{
	GENERATED_BODY()

	/*// Handle player skill rating updates
	UFUNCTION(Server, Reliable)
	void UpdatePlayerRating(APlayerState* Player, int32 NewRating);
    
	// Match result reporting
	UFUNCTION(Server, Reliable)
	void ReportMatchResult(const TArray<APlayerState*>& Winners);*/
    
protected:
	// Rating calculation logic
	/*UPROPERTY(EditDefaultsOnly)
	TSubclassOf<class USkillRatingComponent> RatingSystemClass;*/
};
