// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/GameState.h"
#include "CalamityCapturePointGameState.generated.h"

UENUM(BlueprintType)
enum class ETeam : uint8
{
    None,
    TeamA,
    TeamB
};

USTRUCT(BlueprintType)
struct FCapturePoint
{
    GENERATED_BODY()
    UPROPERTY(BlueprintReadWrite)
    FVector Location = FVector::ZeroVector;
    UPROPERTY(BlueprintReadWrite)
    ETeam ControllingTeam = ETeam::None;
    UPROPERTY(BlueprintReadWrite)
    float CaptureProgress = 0.f;
    UPROPERTY(BlueprintReadWrite)
    int TeamAPlayers = 0;
    UPROPERTY(BlueprintReadWrite)
    int TeamBPlayers = 0;
};

/**
 * Game State for Capture Point game mode
 */
UCLASS()
class CALAMITYCORE_API ACalamityCapturePointGameState : public AGameState
{
    GENERATED_BODY()

public:
    ACalamityCapturePointGameState();

public:
    UPROPERTY(Replicated, BlueprintReadOnly)
    TArray<FCapturePoint> CapturePoints;

    UPROPERTY(Replicated, BlueprintReadOnly)
    float TeamAScore = 0.f;
    UPROPERTY(Replicated, BlueprintReadOnly)
    float TeamBScore = 0.f;

    UFUNCTION(BlueprintCallable)
    void UpdateCapturePoint(int32 PointIndex, ETeam Team, int32 PlayerCount);

protected:
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;
    void Tick(float DeltaTime) override;

private:
    const float CaptureRate = 0.1f; // Adjust as needed
    const float MaxCaptureProgress = 100.f;
};