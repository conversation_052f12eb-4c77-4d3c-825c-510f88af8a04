// Copyright Calamity Game, Inc. All Rights Reserved.
#pragma once

#include "CoreMinimal.h"
#include "GameFramework/NinjaInventoryItem.h"
#include "CalamityInventoryItem.generated.h"

class UCalamityItemFragment;
class UCalamityItemData;
class ACalamityWorldItem;

/**
 * Calamity implementation of the NinjaInventoryItem
 * Extends the base NinjaInventory system with Calamity-specific functionality
 */
UCLASS(BlueprintType, Blueprintable)
class CALAMITYCORE_API UCalamityInventoryItem : public UNinjaInventoryItem
{
    GENERATED_BODY()

public:
    UCalamityInventoryItem(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get());

    // -- Begin UNinjaInventoryItem overrides
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;
    // -- End UNinjaInventoryItem overrides

    // Reference to the item data asset
    //UPROPERTY(BlueprintReadOnly, Category = "Item")
    //UCalamityItemData* ItemData;
    
    // Unique ID for this specific item instance
    UPROPERTY(BlueprintReadOnly, Category = "Item")
    FGuid InstanceID;
    
    // Runtime fragments for this instance
    UPROPERTY(BlueprintReadOnly, Category = "Fragments")
    TMap<FName, UCalamityItemFragment*> FragmentMap;
    
    /**
     * Gets the Calamity item data associated with this item, if available
     * @return The Calamity item data, or nullptr if not available
     */
    UFUNCTION(BlueprintPure, Category = "Calamity|Inventory|Item")
    UCalamityItemData* GetCalamityItemData() const;

    /**
     * Initializes this item with Calamity-specific data
     * @param CalamityData The Calamity item data to initialize with
     */
    UFUNCTION(BlueprintCallable, BlueprintAuthorityOnly, Category = "Calamity|Inventory|Item")
    void InitializeFromItemData(UCalamityItemData* CalamityData);

    /**
     * Converts a Calamity item instance to a Ninja inventory item
     * @param CalamityInventoryItem The Calamity item instance to convert
     * @return The created Ninja inventory item
     */
    UFUNCTION(BlueprintCallable, Category = "Calamity|Inventory|Item")
    static UCalamityInventoryItem* FromCalamityItemInstance(UCalamityInventoryItem* ItemInstance);

    // Check if the item has a specific tag
    UFUNCTION(BlueprintCallable, Category = "Item")
    bool HasTag(const FGameplayTag& Tag) const;

    // Get all tags for this item (from data and all fragments)
    UFUNCTION(BlueprintCallable, Category = "Item")
    void GetAllTags(FGameplayTagContainer& OutTagContainer) const;

    // Get the display name for this item instance
    UFUNCTION(BlueprintCallable, Category = "Item")
    FText GetDisplayName() const;

    // Get the description for this item instance
    UFUNCTION(BlueprintCallable, Category = "Item")
    FText GetDescription() const;

    // Get the icon for this item instance
    UFUNCTION(BlueprintCallable, Category = "Item")
    UTexture2D* GetIcon() const;

    UFUNCTION(BlueprintCallable, Category = "Item")
    UPrimaryDataAsset* GetItemData();

    // Helper to get a specific fragment by class
    template<class T>
    T* GetFragment() const
    {
        // Removed static assert as it's causing issues
        // We'll rely on the compiler's type system instead
        
        for (const TPair<FName, UCalamityItemFragment*>& Pair : FragmentMap)
        {
            if (T* TypedFragment = Cast<T>(Pair.Value))
            {
                return TypedFragment;
            }
        }
        
        return nullptr;
    }
    
    // Helper to find a specific fragment by class (alias for GetFragment for compatibility)
    template<class T>
    T* FindFragmentByClass() const
    {
        return GetFragment<T>();
    }
    
    // Reference to the original Calamity item data, if this item was created from one
    UPROPERTY(Replicated)
    TObjectPtr<UCalamityItemData> CalamityItemData;
};
