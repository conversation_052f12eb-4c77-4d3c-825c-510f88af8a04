// CalamityInteractionComponent.h - Component handling player interactions with objects in the world

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "GameplayTagContainer.h"
#include "Interfaces/ICalamityInteractionInterface.h"
#include "Interfaces/ICalInteractableInterface.h"
#include "CalamityInteractionComponent.generated.h"

class AActor;
class ACalamityCharacter;
class UMaterialInterface;
class USphereComponent;

/** Structure to store interaction data */
USTRUCT(BlueprintType)
struct FInteractionData
{
    GENERATED_BODY()
    
    // --- Interaction Target ---
    /** Interface pointer for convenience */
    UPROPERTY(BlueprintReadOnly, Category = "Interaction|Target")
    TScriptInterface<ICalInteractableInterface> InteractableInterface = nullptr;

    /** Actor we're currently interacting with */
    UPROPERTY(BlueprintReadOnly, Category = "Interaction|Target")
    AActor* InteractableActor = nullptr;

    /** World location where the interaction occurs */
    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Interaction|Target", meta = (AllowPrivateAccess = "true"))
    FVector InteractionWorldLocation = FVector::ZeroVector;
    

    // --- Interaction Prompt & Type ---
    /** The prompt to display for this interaction */
    UPROPERTY(BlueprintReadOnly, Category = "Interaction|Prompt & Type")
    FText InteractionPrompt = FText::FromString(TEXT("'E' to Pick Up"));

    /** Whether this interaction requires holding the interaction button */
    UPROPERTY(BlueprintReadOnly, Category = "Interaction|Prompt & Type")
    bool bRequiresHold = false;

    /** The time required to hold the button for (if bRequiresHold is true) */
    UPROPERTY(BlueprintReadOnly, Category = "Interaction|Prompt & Type")
    float HoldTime = 0.0f;
    

    // --- Interaction Status & Priority ---
    /** Whether we can actually interact with this object */
    UPROPERTY(BlueprintReadOnly, Category = "Interaction|Status & Priority")
    bool bCanInteract = false;

    /** The priority of this interaction (higher = more important) */
    UPROPERTY(BlueprintReadOnly, Category = "Interaction|Status & Priority")
    int32 Priority = 0;
    
    /** Resets interaction data */
    void Reset()
    {
        InteractableActor = nullptr;
        InteractableInterface = nullptr;
        InteractionPrompt = FText::GetEmpty();
        InteractionWorldLocation = FVector::ZeroVector;
        bCanInteract = false;
        bRequiresHold = false;
        HoldTime = 0.0f;
        Priority = 0;
    }
    
    /** Check if data is valid */
    bool IsValid() const
    {
        return InteractableActor != nullptr && InteractableInterface.GetInterface() != nullptr;
    }
};

/** Delegates for interaction events */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnInteractableFound, AActor*, InteractableActor);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnInteractableLost);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnInteractionCompleted, AActor*, InteractedActor);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnInteractionPromptChanged, const FText&, NewPrompt, bool, bRequiresHold);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnInteractionHoldProgress, float, Progress);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnToggleInteractabilitySignature, bool, On);

/**
 * Component handling interactions with objects in the game world
 */
UCLASS(BlueprintType, ClassGroup = (Calamity), meta = (BlueprintSpawnableComponent))
class CALAMITYCORE_API UCalamityInteractionComponent : public UActorComponent, public ICalInteractableInterface
{
    GENERATED_BODY()

public:    
    
    UCalamityInteractionComponent();

protected:
    
    virtual void BeginPlay() override;
    virtual void InitializeComponent() override;
    
    /** Whether we are activated */
    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Interaction|Settings", meta = (AllowPrivateAccess = "true"))
	bool bIsActivated = false;

    /** Whether we've found an interactable */
    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Interaction|Settings", meta = (AllowPrivateAccess = "true"))
    bool bIsInteractable = false;

    /** Size of the interaction sphere */
    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Interaction|Settings", meta = (AllowPrivateAccess = "true"))
    float InteractionSphereRadius = 50.f;
    
    /** Object types to interact with */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Interaction|Settings")
    TArray<TEnumAsByte<EObjectTypeQuery>> InteractionObjectTypes;
    
    /** Array of object types to ignore */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Interaction|Settings")
    TArray<TEnumAsByte<EObjectTypeQuery>> IgnoreObjectTypes;

    /** Time between checks for orientation change */
    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Interaction|Settings", meta = (AllowPrivateAccess = "true"))
    FTimerHandle OrientationCheckTimer;
    
    /** What object channels should block the trace */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Interaction|Settings|Trace")
    TEnumAsByte<ECollisionChannel> TraceChannel = ECC_Visibility;
    
    /** The radius of the sphere trace, if using a sphere trace instead of a line trace */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Interaction|Settings|Trace")
    float TraceRadius = 10.0f;
    
    /** Whether to use a sphere trace instead of a line trace */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Interaction|Settings|Trace")
    bool bUseSphereTrace = true;

    /** Whether we should trace every frame or only when moving */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Interaction|Settings|Trace")
    bool bTraceEveryFrame = false;

    /** Line trace parameters for more precise interaction detection */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Interaction|Settings|Trace")
    float InteractionTraceDistance = 200.0f;
    
    /** Whether to trace from camera (true) or character (false) */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Interaction|Settings|Trace")
    bool bTraceFromCamera = false;

    /** Whether to show debug lines for interaction traces */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Interaction|Debug")
    bool bShowDebugTrace = false;

    /** Data about the current interactable object */
    UPROPERTY(BlueprintReadOnly, Category = "Interaction|Runtime")
    FInteractionData CurrentInteraction;
    
    /** Track the hold start time */
    UPROPERTY(BlueprintReadOnly, Category = "Interaction|Runtime")
    float InteractionHoldStartTime = 0.0f;
    
    /** Whether we're currently holding the interaction button */
    UPROPERTY(BlueprintReadOnly, Category = "Interaction|Runtime")
    bool bIsHoldingInteract = false;

    /** Cached owner as a character */
    UPROPERTY(BlueprintReadOnly, Category = "Interaction|Cache Data")
    ACalamityCharacter* OwnerCharacterCached = nullptr;

public:    
    /** Called every frame */
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    /** Interaction sphere for detecting interactable objects */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Interaction|Cache Data", meta = (AllowPrivateAccess = "true"))
    USphereComponent* InteractionSphere;
    
    /** Cache all potential interactable objects */
    UPROPERTY(BlueprintReadOnly, Category = "Interaction|Cache Data")
    TArray<AActor*> PotentialInteractables;

    /** Timer handle for holding interactions */
    FTimerHandle HoldTimerHandle;
    
    /** Begin interaction with current interactable */
    UFUNCTION(BlueprintCallable, Category = "Interaction")
    void BeginInteract();
    
    /** End interaction with current interactable (release button) */
    UFUNCTION(BlueprintCallable, Category = "Interaction")
    void EndInteract();
    
    /** Perform the interaction directly */
    UFUNCTION(BlueprintCallable, Category = "Interaction")
    bool PerformInteraction();

    /** Get the currrent orientation of the owner */
    UFUNCTION(BlueprintCallable, Category = "Interaction")
    FVector CheckOwnerOrientation() const;
    
    /** Get currently focused interactable actor */
    UFUNCTION(BlueprintCallable, Category = "Interaction")
    AActor* GetInteractable() const;
    
    /** Check if current interaction requires holding */
    UFUNCTION(BlueprintCallable, Category = "Interaction")
    bool DoesRequireHold() const;
    
    /** Get hold progress (0.0 to 1.0) */
    UFUNCTION(BlueprintCallable, Category = "Interaction")
    float GetHoldProgress() const;
    
    /** Get interaction sphere radius */
    UFUNCTION(BlueprintCallable, Category = "Interaction")
    float GetInteractionRadius() const;
    
    /** Set interaction sphere radius */
    UFUNCTION(BlueprintCallable, Category = "Interaction")
    void SetInteractionRadius(float NewRadius);
    
    /** Force update of interaction detection */
    UFUNCTION(BlueprintCallable, Category = "Interaction")
    void UpdateInteractionDetection();

    /** Enable interactability for this object */
    UFUNCTION(BlueprintCallable, Category = "Interaction")
    void EnableInteractability();

    /** Disable interactability for this object */
    UFUNCTION(BlueprintCallable, Category = "Interaction")
    void DisableInteractability();

    /** Toggle interactability state */
    UFUNCTION(BlueprintCallable, Category = "Interaction")
    void ToggleInteractability();

    /** Check if this object is currently interactable */
    UFUNCTION(BlueprintPure, Category = "Interaction")
    bool IsInteractable() const;
    
    /** Delegates */
    UPROPERTY(BlueprintAssignable, Category = "Interaction")
    FOnInteractableFound OnInteractableFound;
    UPROPERTY(BlueprintAssignable, Category = "Interaction")
    FOnInteractableLost OnInteractableLost;
    UPROPERTY(BlueprintAssignable, Category = "Interaction")
    FOnInteractionCompleted OnInteractionCompleted;
    UPROPERTY(BlueprintAssignable, Category = "Interaction")
    FOnInteractionPromptChanged OnInteractionPromptChanged;
    UPROPERTY(BlueprintAssignable, Category = "Interaction")
    FOnInteractionHoldProgress OnInteractionHoldProgress;
    UPROPERTY(BlueprintAssignable, Category = "Interaction")
    FOnToggleInteractabilitySignature OnToggleInteractability;

    /** Get current interaction priority */
    //UFUNCTION(BlueprintCallable, Category = "Interaction")
    virtual int32 GetInteractionPriority_Implementation(AActor* InteractedActor) const;

protected:
    virtual void OnRegister() override;
    
    /** Find best interactable from all potential interactables */
    void FindBestInteractable();
    /** Update current interaction data */
    void UpdateCurrentInteraction(AActor* NewInteractableActor);
    /** Clear current interaction */
    void ClearCurrentInteraction();
    /** Perform line trace for interaction */
    AActor* PerformInteractionTrace();
    /** Handle timer for hold interactions */
    void HandleHoldInteraction() const;
    /** Complete a hold interaction */
    void CompleteHoldInteraction();
    /** Callbacks for interaction sphere */
    UFUNCTION()
    void OnInteractionSphereBeginOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);
    UFUNCTION()
    void OnInteractionSphereEndOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex);
    /** Get character owner reference */
    UFUNCTION(BlueprintCallable, Category = "Interaction")
    ACalamityCharacter* GetCalamityCharacterOwner() const;
    /** Blueprint event called when interaction prompt changes */
    UFUNCTION(BlueprintImplementableEvent, Category = "Interaction")
    void OnInteractionPromptUpdated(const FText& NewPrompt, bool bRequiresHold);
    /** Blueprint event called when interaction completes */
    UFUNCTION(BlueprintImplementableEvent, Category = "Interaction")
    void OnInteractionComplete(AActor* InteractableActor);
    
};