// MeleeStatsFragment.h - Fragment for melee-specific weapon stats

#pragma once

#include "CoreMinimal.h"
#include "CalamityItemFragment.h"
#include "NiagaraSystem.h"
#include "CalamityMeleeStatsFragment.generated.h"

// Melee weapon types
UENUM(BlueprintType)
enum class EMeleeWeaponType : uint8
{
    Sword UMETA(DisplayName = "Sword"),
    Axe UMETA(DisplayName = "Axe"),
    Mace UMETA(DisplayName = "Mace"),
    Spear UMETA(DisplayName = "Spear"),
    Dagger UMETA(DisplayName = "Dagger"),
    Fist UMETA(DisplayName = "Fist Weapon"),
    Staff UMETA(DisplayName = "Staff"),
    Polearm UMETA(DisplayName = "Polearm"),
    Shield UMETA(DisplayName = "Shield"),
    Special UMETA(DisplayName = "Special"),
    Gauntlet UMETA(DisplayName = "Gauntlet"),
};

// Melee weapon stats
USTRUCT(BlueprintType)
struct FMeleeStats
{
    GENERATED_BODY()
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Melee Properties")
    EMeleeWeaponType MeleeType = EMeleeWeaponType::Sword;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Melee Properties|Damage")
    float BaseDamage = 30.0f;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Melee Properties|Damage")
    float CriticalHitChance = 0.15f;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Melee Properties|Damage")
    float CriticalDamageMultiplier = 1.5f;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Melee Properties|Combat")
    float AttackSpeed = 1.0f;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Melee Properties|Combat")
    float Reach = 200.0f;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Melee Properties|Combat")
    float StaminaCost = 10.0f;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Melee Properties|Combat")
    float KnockbackForce = 500.0f;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Melee Properties|Effects")
    UAnimMontage* LightAttackMontage = nullptr;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Melee Properties|Effects")
    UAnimMontage* HeavyAttackMontage = nullptr;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Melee Properties|Effects")
    UAnimMontage* SpecialAttackMontage = nullptr;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Melee Properties|Effects")
    USoundBase* SwingSound = nullptr;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Melee Properties|Effects")
    USoundBase* ImpactSound = nullptr;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Melee Properties|Effects")
    UParticleSystem* SwingEffect = nullptr;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Melee Properties|Effects")
    UParticleSystem* ImpactEffect = nullptr;
    
    // Additional properties as needed
};

/**
 * Fragment for melee-specific weapon stats
 */
UCLASS()
class CALAMITYCORE_API UMeleeStatsFragment : public UCalamityItemFragment
{
    GENERATED_BODY()
    
public:
    UMeleeStatsFragment();
    
    // Melee weapon type
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Melee Stats")
    EMeleeWeaponType MeleeType;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Melee Stats")
    FMeleeStats MeleeStats;
    
    // Swing speed (attacks per second)
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Melee Stats")
    float SwingSpeed;
    
    // Attack reach/range
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Melee Stats")
    float Reach;
    
    // How heavy the weapon feels (affects stamina usage and animation speed)
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Melee Stats")
    float WeaponWeight;
    
    // Stamina cost per attack
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Melee Stats")
    float StaminaCost;
    
    // Deflection/parry window (in seconds)
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Melee Stats")
    float ParryWindow;
    
    // Combo multiplier (damage increase per consecutive hit)
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Melee Stats")
    float ComboMultiplier;
    
    // Maximum combo count
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Melee Stats")
    int32 MaxComboCount;
    
    // Chance to stagger an enemy
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Melee Stats")
    float StaggerChance;
    
    // Damage multiplier for charged attacks
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Melee Stats")
    float ChargedAttackMultiplier;
    
    // Maximum charge time (in seconds)
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Melee Stats")
    float MaxChargeTime;
    
    // Whether this weapon can be dual-wielded
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Melee Stats")
    bool bCanDualWield;
    
    // Bonus damage when dual-wielding
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Melee Stats")
    float DualWieldDamageMultiplier;
    
    // Effect for weapon trail
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Melee Stats|VFX")
    UNiagaraSystem* WeaponTrailEffect;
    
    // Impact effect when hitting something
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Melee Stats|VFX")
    UNiagaraSystem* ImpactEffect;
    
    // Sound for swinging the weapon
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Melee Stats|Sounds")
    USoundBase* SwingSound;
    
    // Sound for hitting something
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Melee Stats|Sounds")
    USoundBase* ImpactSound;

    // Animation montages for weapon actions (common to all weapons)
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Weapon|Animations")
    TArray<UAnimMontage*> AttackMontages;
    
    // Calculate stamina cost for an attack
    UFUNCTION(BlueprintCallable, Category = "Melee Stats")
    float CalculateStaminaCost(bool bIsHeavyAttack = false, bool bIsChargedAttack = false) const;
    
    // Calculate combo damage multiplier
    UFUNCTION(BlueprintCallable, Category = "Melee Stats")
    float GetComboDamageMultiplier(int32 ComboCount) const;
    
    // Calculate damage for a charged attack
    UFUNCTION(BlueprintCallable, Category = "Melee Stats")
    float CalculateChargedDamage(float ChargePercent, float BaseDamage) const;
};