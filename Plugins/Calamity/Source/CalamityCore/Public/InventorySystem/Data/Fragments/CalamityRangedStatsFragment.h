// RangedStatsFragment.h - Fragment for ranged-specific weapon stats

#pragma once

#include "CoreMinimal.h"
#include "CalamityItemFragment.h"
#include "NiagaraSystem.h"
#include "Curves/CurveVector.h"
#include "InventorySystem/Data/CalamitySharedTypes.h"
#include "CalamityRangedStatsFragment.generated.h"

/** Ranged weapon types */
UENUM(BlueprintType)
enum class ERangedWeaponType : uint8
{
    Handgun UMETA(DisplayName = "Handgun"),
    HandCannon UMETA(DisplayName = "Hand Cannon"),
    AssaultRifle UMETA(DisplayName = "Assault Rifle"),
    BurstRifle UMETA(DisplayName = "Burst Rifle"),
    ScoutRifle UMETA(DisplayName = "Scout Rifle"),
    Shotgun UMETA(DisplayName = "Shotgun"),
    SniperRifle UMETA(DisplayName = "Sniper Rifle"),
    FuseRifle UMETA(DisplayName = "Fusion Rifle"),
    Machine<PERSON>un UMETA(DisplayName = "Machine Gun"),
    SMG UMETA(DisplayName = "Submachine Gun"),
    Bow UMETA(DisplayName = "Bow"),
    Crossbow UMETA(DisplayName = "Crossbow"),
    RocketLauncher UMETA(DisplayName = "Rocket Launcher"),
    GrenadeLauncher UMETA(DisplayName = "Grenade Launcher"),
    Cannon UMETA(DisplayName = "Cannon"),
    Special UMETA(DisplayName = "Special")
};

/**Fire modes */
UENUM(BlueprintType)
enum class ECFireMode : uint8
{
    Semi UMETA(DisplayName = "Single Shot"),
    Burst UMETA(DisplayName = "Burst Fire"),
    Auto UMETA(DisplayName = "Automatic"),
    Charge UMETA(DisplayName = "Charge Shot")
};


/** Aiming behavior stats */
USTRUCT(BlueprintType)
struct FAimingBehavior
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Aiming Behavior")
    float AimSpread = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Aiming Behavior")
    float AimZoomMultiplier = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Aiming Behavior")
    bool bUseScopedView = false;

    /** Whether this weapon can use ADS (Aim Down Sights) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Aiming Behavior")
    bool bCanADS = true;
    
    /** ADS FOV override (0 means use default) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Aiming Behavior")
    float ADSFOV = 60.0f;
    
    /** Draw/ready time (in seconds) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Aiming Behavior")
    float DrawTime = 0.5f;
    
    /** ADS (Aim Down Sights) time (in seconds) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Aiming Behavior")
    float ADSTime = 0.3f;
    
    /** ADS movement speed multiplier */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Aiming Behavior")
    float ADSMovementSpeedMultiplier = 0.8f;
};

/** Fire behavior stats */
USTRUCT(BlueprintType)
struct FFireBehavior
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fire Behavior")
    float FireRate = 0.0f;

    /** Number of bullets per burst for Burst fire mode. */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fire Behavior|Burst Fire")
    int32 BurstCount = 3;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fire Behavior|Burst Fire")
    float BurstFireRate = 0.0f;
    
    /** Time in seconds to fully charge for Charge Shot mode. */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fire Behavior|Charge Shot")
    float ChargeTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fire Behavior|Charge Shot")
    float CurrentChargeTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fire Behavior|Charge Shot")
    UCurveFloat* ChargeSpeedCurve = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fire Behavior|Charge Shot")
    bool bUseChargeSpeedCurve = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fire Behavior")
    ECFireMode FireMode = ECFireMode::Semi;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fire Behavior")
    TArray<ECFireMode> AvailableFireModes;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fire Behavior")
    bool bHasAlternateFireMode = false;

    /** Whether this weapon will preform the current fire mode automatically e.g. keep cycling fire mode while button is held down */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fire Behavior")
    bool bAutomatic = false;
};

/** Reload state */
USTRUCT(BlueprintType)
struct FWeaponReloadState
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    EReloadType ReloadType = EReloadType::Magazine;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    EReloadStage ReloadStage = EReloadStage::NotReloading;
};

/**Reload stats */
USTRUCT(BlueprintType)
struct FReloadStats
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reload")
    float TotalReloadTime = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reload")
    float ReloadStartTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reload")
    float CurrentReloadTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reload")
    bool bIsReloading = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reload")
    FWeaponReloadState ReloadState = FWeaponReloadState();

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gun Properties")
    USoundBase* ReloadSound = nullptr;
};

/** Accuracy stats */
USTRUCT(BlueprintType)
struct FAccuracyStats
{
    GENERATED_BODY()

    /** Base accuracy (0.0 to 1.0) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Accuracy")
    float BaseAccuracy = 0.9f;
    
    /** Hip fire accuracy penalty */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Accuracy")
    float HipFirePenalty = 0.2f;
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Accuracy")
    float CurrentSpread = 0.0f;
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Accuracy")
    float BaseSpread = 0.0f;
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Accuracy")
    float MinSpread = 0.1f;
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Accuracy")
    float MaxSpread = 5.0f;
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Accuracy")
    int32 PelletCount = 1;

    /** Spread angle (in degrees) - how much bullets deviate from center */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Accuracy")
    float SpreadAngle = 3.0f;
    
    /** Current Projectile spread (degrees) */
    UPROPERTY(VisibleAnywhere, BlueprintReadWrite, Category = "Accuracy")
    float CurrentProjectileSpread = 1.0f;

    /** Maximum Projectile spread */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Accuracy")
    float MaxProjectileSpread = 5.0f;
};

/** Recoil stats */
USTRUCT(BlueprintType)
struct FRecoilStats
{
    GENERATED_BODY()
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recoil")
    float RecoilPerShot = 1.0f;
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recoil")
    float RecoilRecoveryRate = 5.0f;
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recoil")
    float MaxRecoil = 10.0f;
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recoil")
    UCurveVector* RecoilPattern = nullptr;
    /** Current recoil offset */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Recoil")
    float CurrentRecoil = 0.0f;
};

/** Ballistics and range stats */
USTRUCT(BlueprintType)
struct FBallisticsStats
{
    GENERATED_BODY()
    
    /** Effective range (meters) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ballistics")
    float MaxRange = 1000.0f;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ballistics")
    float EffectiveRange = 300.0f;

    /** Projectile velocity (meters per second) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ballistics")
    FVector2D ProjectileVelocity = FVector2D::Zero();
    
    /** Projectile initial speed */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ballistics")
    float ProjectileInitialSpeed = 4000.0f;
    
    /** Projectile max speed */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ballistics")
    float ProjectileMaxSpeed = 6000.0f;

    /** Projectile acceleration (meters per second) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ballistics")
    float ProjectileAcceleration = 800.0f;

    /** Damage falloff beyond effective range */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ballistics")
    float DamageFalloff = 0.5f;
    
    /** Damage falloff start distance */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ballistics")
    float DamageFalloffStart = 500.0f;
    
    /** Damage falloff end distance */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ballistics")
    float DamageFalloffEnd = 1000.0f;
    
    /** Minimum damage at maximum falloff (as a percentage of base damage) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ballistics")
    float MinDamagePercent = 0.5f;

    /** Projectile drop rate (meters per second squared) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ballistics")
    float ProjectileDrop = 9.81f;
    
    /** Muzzle velocity (units per second) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ballistics")
    float MuzzleVelocity = 30000.0f;
    
    /** Whether this weapon uses projectiles (false = hitscan) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ballistics")
    bool bUsesProjectiles = false;
    
    /** Projectile class (if using projectiles) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ballistics")
    TSubclassOf<AActor> ProjectileClass = nullptr;

    /** Penetration power (arbitrary units) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ballistics|Penetration")
    float PenetrationPower = 10.0f;

    /** Projectile penetration depth (in centimeters) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ballistics|Penetration")
    float PenetrationDepth = 50.0f;
};

/** Handling stats */
USTRUCT(BlueprintType)
struct FHandlingStats
{
    GENERATED_BODY()
    
    /** Weapon weight */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Handling")
    float WeaponWeight = 5.0f;
    
    /** Movement speed modifier when weapon is equipped */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Handling")
    float MovementSpeedModifier = 0.9f;
    
};

/** Visual and Sound effects */
USTRUCT(BlueprintType)
struct FEffectsStats
{
    GENERATED_BODY()
    
    /** Muzzle flash effect */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    UNiagaraSystem* MuzzleFlash = nullptr;
    
    /** Shell eject effect */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    UNiagaraSystem* ShellEject = nullptr;
    
    /** Tracer/trail effect */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    UNiagaraSystem* TrailEffect = nullptr;
    
    /** Impact effect */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    UNiagaraSystem* ImpactEffect = nullptr;
    
    /** Fire animations */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    UAnimMontage* FireAnimation = nullptr;
    
    /** Fire sound */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    USoundBase* FireSound = nullptr;

    /** Crosshair widget class */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects|UI")
    UTexture2D* CrosshairTexture = nullptr;
    
    /** Noise level */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    float NoiseLevel = 1.0f; // Arbitrary units
    
    /** Flash suppression */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    bool bHasFlashSuppressor = false;
    
    /** Camera shake when firing */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    TSubclassOf<UCameraShakeBase> FireCameraShake = nullptr;
};

/** Damage stats structure for weapon damage properties */
USTRUCT(BlueprintType)
struct FDamageStats
{
    GENERATED_BODY()
    
    /** Base damage per shot */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage")
    float BaseDamage = 25.0f;
    
    /** Damage multiplier for special conditions */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage")
    float DamageMultiplier = 1.0f;
    
    /** Critical hit chance (0.0 to 1.0) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage|Critical")
    float CriticalHitChance = 0.1f;
    
    /** Critical damage multiplier */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage|Critical")
    float CriticalDamageMultiplier = 2.0f;
    
    /** Headshot damage multiplier */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage|Critical")
    float HeadshotMultiplier = 2.0f;
    
    /** Damage type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage")
    TSubclassOf<UDamageType> DamageType = nullptr;
    
    /** Damage falloff start distance */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage|Falloff")
    float DamageFalloffStart = 500.0f;
    
    /** Damage falloff end distance */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage|Falloff")
    float DamageFalloffEnd = 1000.0f;
    
    /** Minimum damage at maximum falloff (as a percentage of base damage) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage|Falloff")
    float MinDamagePercent = 0.5f;
    
    /** Element type for elemental damage */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage|Elemental")
    EElementalType ElementType = EElementalType::None;
    
    /** Elemental damage amount */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage|Elemental")
    float ElementalDamage = 0.0f;
    
    /** Elemental effect chance (0.0 to 1.0) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage|Elemental")
    float ElementalEffectChance = 0.1f;
    
    /** Armor penetration percentage (0.0 to 1.0) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage|Penetration")
    float ArmorPenetration = 0.0f;
    
    /** Shield penetration percentage (0.0 to 1.0) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage|Penetration")
    float ShieldPenetration = 0.0f;
    
    /** Area of effect damage radius (0 for no AOE) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage|AOE")
    float AOERadius = 0.0f;
    
    /** Area of effect damage falloff (1.0 = no falloff, 0.0 = full falloff at edge) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage|AOE")
    float AOEFalloff = 0.5f;
    
    /** Hit reactions */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage|Effects")
    float StaggerChance = 0.0f;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage|Effects")
    float KnockbackForce = 0.0f;

    /** Calculate final damage with all multipliers applied */
    float CalculateFinalDamage(bool bIsCritical = false, bool bIsHeadshot = false) const
    {
        float FinalDamage = BaseDamage * DamageMultiplier;
        
        if (bIsCritical)
        {
            FinalDamage *= CriticalDamageMultiplier;
        }
        
        if (bIsHeadshot)
        {
            FinalDamage *= HeadshotMultiplier;
        }
        
        return FinalDamage;
    }
    
    /** Calculate damage at a specific distance considering falloff */
    float CalculateDamageAtDistance(float Distance) const
    {
        float BaseDmg = BaseDamage * DamageMultiplier;
        
        /** If within effective range, full damage */
        if (Distance <= DamageFalloffStart)
        {
            return BaseDmg;
        }
        
        /** If beyond maximum range, minimum damage */
        if (Distance >= DamageFalloffEnd)
        {
            return BaseDmg * MinDamagePercent;
        }
        
        /** Linear falloff between start and end distances */
        float FalloffRange = DamageFalloffEnd - DamageFalloffStart;
        float DistanceIntoFalloff = Distance - DamageFalloffStart;
        float FalloffPercent = DistanceIntoFalloff / FalloffRange;
        
        /** Interpolate between full damage and minimum damage */
        return FMath::Lerp(BaseDmg, BaseDmg * MinDamagePercent, FalloffPercent);
    }
};

/** Gun weapon stats */
USTRUCT(BlueprintType)
struct FGunStats
{
    GENERATED_BODY()
    
    /** Component structs */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gun Properties|Accuracy")
    FAccuracyStats AccuracyStats;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gun Properties|Recoil")
    FRecoilStats RecoilStats;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gun Properties|Reload")
    FReloadStats ReloadStats;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gun Properties|Ballistics")
    FBallisticsStats BallisticsStats;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gun Properties|Effects")
    FEffectsStats EffectsStats;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gun Properties|Handling")
    FHandlingStats HandlingStats;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gun Properties|Aiming")
    FAimingBehavior AimingBehavior;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gun Properties|Firing")
    FFireBehavior FireBehavior;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gun Properties|Damage")
    FDamageStats DamageStats;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gun Properties")
    UNiagaraSystem* ImpactEffect = nullptr;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gun Properties")
    USoundBase* EmptySound = nullptr;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gun Properties")
    UAnimMontage* FireMontage = nullptr;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gun Properties")
    UAnimMontage* ReloadMontage = nullptr;
    
};

/**
 * Fragment for ranged-specific weapon stats
 */
UCLASS()
class CALAMITYCORE_API URangedStatsFragment : public UCalamityItemFragment
{
    GENERATED_BODY()
    
public:
    URangedStatsFragment();
    
    /** Ranged weapon type */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Ranged Stats")
    ERangedWeaponType RangedType;

    /** Main gun stats struct containing all organized properties */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Ranged Stats")
    FGunStats GunStats;
    
    /** Socket names */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Ranged Stats|Sockets")
    FName MuzzleSocketName;
    
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Ranged Stats|Sockets")
    FName ShellEjectSocketName;
    
    /** Directly exposed properties for easier/backward compatible access */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Ranged Stats|VFX")
    UNiagaraSystem* MuzzleFlash;
    
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Ranged Stats|VFX")
    UNiagaraSystem* ShellEject;
    
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Ranged Stats|VFX")
    UNiagaraSystem* TrailEffect;
    
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Ranged Stats|Sounds")
    USoundBase* FireSound;

    UPROPERTY(EditDefaultsOnly, BlueprintReadWrite, Category = "Ranged Stats|Sounds")
    USoundBase* ReloadSound = nullptr;
    
    /** Calculate damage at distance */
    UFUNCTION(BlueprintCallable, Category = "Ranged Stats")
    float CalculateDamageAtDistance(float Range) const;
    
    /** Calculate spread for the current shot */
    UFUNCTION(BlueprintCallable, Category = "Ranged Stats")
    float CalculateSpread(bool bIsAiming, bool bIsMoving, bool bIsCrouching) const;
    
    /** Get time between shots based on fire rate */
    UFUNCTION(BlueprintCallable, Category = "Ranged Stats")
    float GetTimeBetweenShots() const;
    
    /** Get headshot damage */
    UFUNCTION(BlueprintCallable, Category = "Ranged Stats")
    float CalculateHeadshotDamage(float BaseDamage) const;
};