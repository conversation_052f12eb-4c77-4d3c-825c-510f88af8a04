// Copyright Calamity Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameplayTagContainer.h"
#include "NativeGameplayTags.h"

namespace CalamityTags
{
	CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(None);
	CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(All);
	CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Any);
	CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(AnyContext);
	CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(AnyContext_Weapon);
	CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(AnyContext_Weapon_Ranged);
	CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(AnyContext_Weapon_Melee);

	namespace ActionTags
	{
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Action);
		
	}

	namespace AbilityTags
	{
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Ability);
		
		// Telekinesis Tags
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Ability_Telekinesis);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Ability_Telekinesis_Active);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Ability_Telekinesis_Cooldown);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Ability_Telekinesis_GrabThrow);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Ability_Telekinesis_Grabbing);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Ability_Telekinesis_Holding);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Ability_Telekinesis_Throwing);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Ability_Telekinesis_Pull);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Ability_Telekinesis_Pull_Active);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Ability_Telekinesis_Pull_Cooldown);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Ability_Telekinesis_Pull_Effect);
	}

	namespace StateTags
	{
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(State);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(State_Idle);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(State_Moving);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(State_Jumping);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(State_Attacking);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(State_Attacked);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(State_Blocking);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(State_Dashing);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(State_Stunned);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(State_Alive);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(State_Dead);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(State_Grabbing);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(State_Grabbed);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(State_Holding);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(State_Held);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(State_Throwing);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(State_Thrown);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(State_Climbing);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(State_Consuming);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(State_Consumed);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(State_TelekineticGrabbed);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(State_TelekineticPulling);
	}
	
	namespace EffectTags
	{
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Effect);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Effect_Damage);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Effect_Damage_Blocked);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Effect_Damage_Critical);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Effect_Damage_Physical);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Effect_Damage_Rift);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Effect_Damage_Radiance);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Effect_Damage_Fire);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Effect_Damage_Ice);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Effect_Damage_Lightning);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Effect_Damage_Poison);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Effect_Damage_Melee);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Effect_Damage_Ranged);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Effect_Damage_Mitigated);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Effect_Damage_Recurring);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Effect_Damage_Stagger);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Effect_Damage_Knockback);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Effect_Movement_Pulled);
		
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(StatusEffect);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(StatusEffect_Bleeding);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(StatusEffect_Burning);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(StatusEffect_Frozen);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(StatusEffect_Shocked);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(StatusEffect_Poisoned);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(StatusEffect_Stunned);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(StatusEffect_Immobilized);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(StatusEffect_Weakened);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(StatusEffect_Enraged);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(StatusEffect_Hasted);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(StatusEffect_Healed);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(StatusEffect_Movement);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(StatusEffect_Movement_Slowed);
	}

	namespace ElementTags
	{
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Element);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Element_None);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Element_Rift);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Element_Radiance);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Element_Fire);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Element_Ice);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Element_Lightning);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Element_Wind);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Element_Earth);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Element_Water);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Element_Light);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Element_Dark);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Element_Physical);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Element_Wood);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Element_Metal);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Element_Spirit);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Element_Energy);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Element_Acid);
	}
	
	namespace EncounterTags
	{
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Encounter);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Encounter_OutCome_Won);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Encounter_OutCome_Loss);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Encounter_Player);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Encounter_Enemy);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Encounter_Ally);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Encounter_Objective);
	}

	namespace EnvironmentTags
	{
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Environment);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Environment_Outdoors);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Environment_Indoors);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Environment_Day);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Environment_Night);
	}

	namespace InputTags
	{
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Input);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Input_Move);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Input_Look);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Input_MoveForward);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Input_MoveRight);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Input_Jump);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Input_Dash);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Input_Sprint);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Input_Crouch);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Input_Strafe);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Input_Interact);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Input_Inventory);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Input_NextWeapon);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Input_PrevWeapon);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Input_Reload);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Input_Grab);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Input_Consume);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Input_Throw);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Input_Slam);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Input_Attack);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Input_Block);
	}
	namespace CombatTags
	{
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Combat);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Combat_CanDefend);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Combat_CanAttack);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Combat_CanBlock);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Combat_CanDash);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Combat_Component_WeaponTrail);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Combat_Component_ProjectileSource);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Combat_Component_MeleeScanSource);
	}

	namespace MovementTags
	{
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Movement);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Movement_CanMove);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Movement_CanJump);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Movement_CanSprint);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Movement_CanDash);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Movement_CanClimb);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Movement_CanWallRun);
	}

	namespace AbilityActivationTags
	{
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(AbilityActivation);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(AbilityActivation_Activated);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(AbilityActivation_Cancelled);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(AbilityActivation_Failed);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(AbilityActivation_Failed_IsDead)
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(AbilityActivation_Failed_Cooldown)
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(AbilityActivation_Failed_Cost)
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(AbilityActivation_Failed_TagsBlocked);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(AbilityActivation_Failed_TagsMissing);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(AbilityActivation_Failed_Networking);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(AbilityActivation_Failed_ActivationGroup);
	}

	namespace AbilityTraitTags
	{
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(AbilityTrait)
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(AbilityTrait_Active)
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(AbilityTrait_ActivationOnSpawn)
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(AbilityTrait_Persistent)
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(AbilityTrait_Stackable)
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(AbilityTrait_Stackable_Stacking)
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(AbilityTrait_Cooldown)
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(AbilityTrait_Stackable_Stacking_Cost)
	}

	namespace CharacterTags
	{
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Character);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Character_Player);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Character_Player_Hero);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Character_Ai);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Character_Ally);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Character_Vendor);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Character_NonPlayer);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Character_NonPlayer_Ai);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Character_Enemy);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Character_Enemy_Leader);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Character_Enemy_SquadMember);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Character_Enemy_Fodder);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Character_Enemy_Light);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Character_Enemy_Medium);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Character_Enemy_Heavy);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Character_Enemy_Elite);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Character_Enemy_Boss);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Character_Enemy_Summoned);
	}
	
	namespace ItemTags
	{
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Pickupable);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Ranged);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Ranged_Bow);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Ranged_Crossbow);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Ranged_Rifle);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Ranged_Pistol);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Ranged_Shotgun);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Ranged_MachineGun);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Ranged_Sniper);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Ranged_Cannon);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Ranged_Launcher);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Melee);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Melee_OneHanded);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Melee_TwoHanded);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Melee_Thrown);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Melee_Staff);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Melee_Shield);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Melee_Gauntlet);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Melee_Polearm);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Melee_Dagger);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Melee_Sword);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Melee_Axe);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Melee_Mace);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Melee_Spear);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Melee_Fist);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Magic);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Magic_Staff);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Magic_Wand);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Magic_Orb);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Magic_Tome);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Magic_Rune);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Magic_Amulet);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Magic_Relic);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Magic_Special);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Weapon_Magic_Hand);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Consumable);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Armor);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Item_Attachment);
	}

	namespace MultiplayerTags
	{
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Team);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Team_All);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Team_A);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Team_B);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Team_C);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Team_D);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Role);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Role_None);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Role_Tank);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Role_DPS);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Role_Support);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Role_Flex);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Role_Role_Healer);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Role_Role_Scout);
	}

	namespace ObjectTags
	{
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Object);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Object_Traversable);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Object_Grabbable);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Object_Movable);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Object_Interactable);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Object_Destructible);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Object_Pickupable);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Object_Pickupable_Weapon);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Object_Pickupable_Item);
		CALAMITYCORE_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Object_Physics);
	}
}