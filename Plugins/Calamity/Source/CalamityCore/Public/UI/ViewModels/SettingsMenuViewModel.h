// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "MVVMViewModelBase.h"
#include "UI/Data/UserSettingsDefaults.h" // Include for EGraphicsQualityPreset etc. if used directly
#include "SettingsMenuViewModel.generated.h"

// Forward Declarations
class UCalSaveGame;
class UUserSettingsDefaults;

// Enum to represent the different tabs in the settings menu
UENUM(BlueprintType)
enum class ESettingsTab : uint8
{
	General		UMETA(DisplayName = "General"),
	Display		UMETA(DisplayName = "Video"),
	Graphics	UMETA(DisplayName = "Graphics"),
	Audio		UMETA(DisplayName = "Audio"),
	Controls	UMETA(DisplayName = "Controls"),
	Gameplay	UMETA(DisplayName = "Gameplay"),
	Social		UMETA(DisplayName = "Social"),
	Network		UMETA(DisplayName = "Network")
};

// Delegate broadcast when the user requests to close the settings menu
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnSettingsCloseRequested);
// Delegate broadcast after settings have been successfully applied
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnSettingsApplied);

/**
 *  ViewModel for the Settings Menu
 */
UCLASS()
class CALAMITYCORE_API USettingsMenuViewModel : public UMVVMViewModelBase
{
	GENERATED_BODY()

public:
	// --- Initialization ---

	/** Call this after creating the ViewModel to load current settings */
	UFUNCTION(BlueprintCallable, Category = "Settings")
		void InitializeViewModel(const UUserSettingsDefaults* DefaultSettings);

	// --- Tab Management ---

	/** The currently selected settings tab */
	UPROPERTY(BlueprintReadWrite, FieldNotify, Setter, Getter, meta = (AllowPrivateAccess = "true"))
		ESettingsTab CurrentTab = ESettingsTab::General;

	/** Called by UI buttons to switch tabs */
	UFUNCTION(BlueprintCallable, Category = "Settings|Navigation")
		void SelectTab(ESettingsTab NewTab);

	// --- Core Actions ---

	/** Applies all pending setting changes */
	UFUNCTION(BlueprintCallable, Category = "Settings|Actions")
		void ApplySettings();

	/** Reverts settings back to their state when the menu was opened */
	UFUNCTION(BlueprintCallable, Category = "Settings|Actions")
		void RevertSettings();

	/** Requests to close the settings menu (e.g., called by a Back button) */
	UFUNCTION(BlueprintCallable, Category = "Settings|Actions")
		void RequestClose();

	// --- Settings Properties (Examples - Add all relevant settings) ---
	// These properties hold the *current* state being edited in the UI.
	// They are loaded initially and saved on Apply.

	// Video
	UPROPERTY(BlueprintReadWrite, FieldNotify, Setter, Getter, meta = (AllowPrivateAccess = "true"))
		float Brightness = 0.5f;

	UPROPERTY(BlueprintReadWrite, FieldNotify, Setter="SetVSyncEnabled", Getter="GetVSyncEnabled", meta = (AllowPrivateAccess = "true"))
		bool bVSyncEnabled = false;

	// Audio
	UPROPERTY(BlueprintReadWrite, FieldNotify, Setter, Getter, meta = (AllowPrivateAccess = "true", ClampMin = "0.0", ClampMax = "1.0"))
		float MasterVolume = 0.75f;

	UPROPERTY(BlueprintReadWrite, FieldNotify, Setter, Getter, meta = (AllowPrivateAccess = "true", ClampMin = "0.0", ClampMax = "1.0"))
		float MusicVolume = 0.8f;

	UPROPERTY(BlueprintReadWrite, FieldNotify, Setter, Getter, meta = (AllowPrivateAccess = "true", ClampMin = "0.0", ClampMax = "1.0"))
		float SFXVolume = 0.9f;

	// Gameplay
	UPROPERTY(BlueprintReadWrite, FieldNotify, Setter="SetAimAssist", Getter="GetAimAssist", meta = (AllowPrivateAccess = "true"))
		bool bAimAssist = true;

	UPROPERTY(BlueprintReadWrite, FieldNotify, Setter="SetSkipCinematics", Getter="GetSkipCinematics", meta = (AllowPrivateAccess = "true"))
		bool bSkipCinematics = false;

	// Add many more properties for Graphics Quality, Resolution, Controls, etc.

	// --- Events ---

	/** Broadcast when the 'Back' or 'Close' action is triggered */
	UPROPERTY(BlueprintAssignable, Category = "Settings|Events")
		FOnSettingsCloseRequested OnSettingsCloseRequested;

	/** Broadcast after ApplySettings successfully saves changes */
	UPROPERTY(BlueprintAssignable, Category = "Settings|Events")
		FOnSettingsApplied OnSettingsApplied;


private:
	// --- Setters/Getters ---
	// Implement these for each setting property
	void SetCurrentTab(ESettingsTab NewValue);
	ESettingsTab GetCurrentTab() const { return CurrentTab; }

	void SetBrightness(float NewValue);
	float GetBrightness() const { return Brightness; }

	void SetVSyncEnabled(bool bNewValue);
	bool GetVSyncEnabled() const { return bVSyncEnabled; }

	void SetMasterVolume(float NewValue);
	float GetMasterVolume() const { return MasterVolume; }

	void SetMusicVolume(float NewValue);
	float GetMusicVolume() const { return MusicVolume; }

	void SetSFXVolume(float NewValue);
	float GetSFXVolume() const { return SFXVolume; }

	void SetAimAssist(bool bNewValue);
	bool GetAimAssist() const { return bAimAssist; }

	void SetSkipCinematics(bool bNewValue);
	bool GetSkipCinematics() const { return bSkipCinematics; }

	// --- Internal State & Loading/Saving ---

	/** Pointer to the loaded default settings data asset */
	UPROPERTY(Transient) // Doesn't need saving
		TObjectPtr<const UUserSettingsDefaults> CachedDefaultSettings;

	/** Stores the state of settings when the menu was opened, for reverting */
	UPROPERTY(Transient)
		TObjectPtr<UCalSaveGame> SettingsOnOpen; // Use your actual SaveGame class

	/** Loads settings from SaveGame or Defaults into the ViewModel properties */
	void LoadSettings();

	/** Saves the current ViewModel properties to SaveGame and applies engine settings */
	void SaveSettingsToDiskAndApply();

	/** Applies specific engine settings based on ViewModel properties (e.g., calls GEngine->GameUserSettings) */
	void ApplyEngineSettings();

	/** Applies audio settings (e.g., updates sound mixes) */
	void ApplyAudioSettings();

};
