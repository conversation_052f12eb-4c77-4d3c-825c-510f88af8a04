// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "MVVMViewModelBase.h"
#include "QuestViewModel.generated.h"

// Example Enum for Quest State
UENUM(BlueprintType)
enum class EQuestState : uint8
{
	NotAccepted,
	InProgress,
	Completed, // Ready to turn in
	Claimed    // Reward received
};

// Example Struct for Quest Data
USTRUCT(BlueprintType)
struct FQuestData
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadOnly, Category="Quest")
	FString QuestID;

	UPROPERTY(BlueprintReadOnly, Category="Quest")
	FText Title;

	UPROPERTY(BlueprintReadOnly, Category="Quest")
	FText Description;

	UPROPERTY(BlueprintReadOnly, Category="Quest")
	FText ObjectiveStatus; // e.g., "0/10 Enemies Defeated"

	UPROPERTY(BlueprintReadOnly, Category="Quest")
	int32 CurrentProgress = 0;

	UPROPERTY(BlueprintReadOnly, Category="Quest")
	int32 TargetProgress = 1;

	UPROPERTY(BlueprintReadOnly, Category="Quest")
	EQuestState CurrentState = EQuestState::NotAccepted;

	// Add reward info (currency, items, XP etc.)
	UPROPERTY(BlueprintReadOnly, Category="Quest")
	int32 XPReward = 0;

	UPROPERTY(BlueprintReadOnly, Category="Quest")
	int32 CurrencyReward = 0;
};

// Delegate for quest actions
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnQuestActionStatus, FText, Message);

/**
 * 
 */
UCLASS(BlueprintType)
class CALAMITYCORE_API UQuestViewModel : public UMVVMViewModelBase
{
	GENERATED_BODY()

public:
	/** Call to load available and active quests */
	UFUNCTION(BlueprintCallable, Category = "Quests")
		void InitializeViewModel();

	// --- Quest Data ---

	/** List of quests currently available to the player (not yet accepted) */
	UPROPERTY(BlueprintReadWrite, FieldNotify, Setter, Getter, meta = (AllowPrivateAccess = "true"))
		TArray<FQuestData> AvailableQuests;

	/** List of quests the player has accepted and are in progress */
	UPROPERTY(BlueprintReadWrite, FieldNotify, Setter, Getter, meta = (AllowPrivateAccess = "true"))
		TArray<FQuestData> ActiveQuests;

	/** Currently selected quest for viewing details */
	UPROPERTY(BlueprintReadWrite, FieldNotify, Setter, Getter, meta = (AllowPrivateAccess = "true"))
		FQuestData SelectedQuest;

	// --- Actions ---

	/** Called when the player clicks a quest to view its details */
	UFUNCTION(BlueprintCallable, Category = "Quests|Actions")
		void SelectQuestToView(const FString& QuestID, bool bIsFromAvailableList);

	/** Accepts the currently selected available quest */
	UFUNCTION(BlueprintCallable, Category = "Quests|Actions")
		void AcceptSelectedQuest();

	/** Abandons the currently selected active quest */
	UFUNCTION(BlueprintCallable, Category = "Quests|Actions")
		void AbandonSelectedQuest();

	/** Claims the reward for the currently selected completed quest */
	UFUNCTION(BlueprintCallable, Category = "Quests|Actions")
		void ClaimSelectedQuestReward();

	/** Refreshes quest data from backend/service */
	UFUNCTION(BlueprintCallable, Category = "Quests|Actions")
		void RefreshQuestData();

	// --- Events ---
	UPROPERTY(BlueprintAssignable, Category = "Quests|Events")
		FOnQuestActionStatus OnQuestActionStatus;


private:
	// Setters/Getters
	void SetAvailableQuests(const TArray<FQuestData>& NewQuests);
	const TArray<FQuestData>& GetAvailableQuests() const { return AvailableQuests; }

	void SetActiveQuests(const TArray<FQuestData>& NewQuests);
	const TArray<FQuestData>& GetActiveQuests() const { return ActiveQuests; }

	void SetSelectedQuest(const FQuestData& NewQuest);
	FQuestData GetSelectedQuest() const { return SelectedQuest; }

	// Internal logic
	void LoadQuests();
	void UpdateQuestProgress(); // Called periodically or on events
	void ExecuteAcceptQuest(const FString& QuestID);
	void ExecuteAbandonQuest(const FString& QuestID);
	void ExecuteClaimReward(const FString& QuestID);

	// Helper to find quest data by ID
	FQuestData* FindQuestByID(const FString& QuestID, bool& bFoundInAvailable, bool& bFoundInActive);
};
