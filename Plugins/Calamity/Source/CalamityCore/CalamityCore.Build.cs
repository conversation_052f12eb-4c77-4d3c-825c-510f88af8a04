using UnrealBuildTool;
using System.IO;

public class CalamityCore : ModuleRules
{
    public CalamityCore(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;
        
        PrivateIncludePaths.Add(ModuleDirectory + "/Private");
        PublicIncludePaths.Add(ModuleDirectory + "/Public");
        
        PublicIncludePaths.AddRange(
            new string[] 
            {
                Path.Combine(ModuleDirectory, "Public"),
                Path.Combine(ModuleDirectory, "Public/InventorySystem"),
                Path.Combine(ModuleDirectory, "Public/InventorySystem/Equipment")
                
                // ... add public include paths required here ...
            }
        );
				
        PrivateIncludePaths.AddRange(
            new string[] {
                // ... add other private include paths required here ...
            }
        );

        // Core module should not depend on any other Calamity modules
        
        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",  
                "CoreUObject",
                "AIModule",
                "AnimationCore",
                "AnimGraphRuntime",
                "AnimationLocomotionLibraryRuntime",
                "AnimationWarpingRuntime",
                "AssetRegistry",
                "BlendStack",
                "Chooser",
                "CommonUI",
                "CustomizableObject", 
                "CoreUObject",
                "DeveloperSettings",
                "Engine",
                "EnhancedInput",
                "GameplayAbilities",
                "GameplayStateTreeModule",
                "GameplayTags",
                "GameplayTasks",
                "InputCore",
                "ModelViewViewModel",
                "ModularGameplay",
                "ModularGameplayActors",
                "MotionWarping",
                "MotionTrajectory",
                "Mover",
                "Networking",
                "Niagara",
                "NinjaCombat",
                "NinjaCombatCamera",
                "NinjaCombatCore",
                "NinjaCombatActorPool",
                "NinjaCombatUI",
                "NinjaGAS",
                "NinjaFactions",
                "NinjaInput",
                "NinjaInventory",
                "NinjaInventoryCore",
                "NinjaInventoryEquipment",
                "NinjaInventoryGameplay",
                "NinjaInventoryUI",
                "PhysicsControl",
                "PoseSearch",
                "RenderCore",
                "RHI",
                "Sockets",
                "StateTreeModule",
                "StateTreeModule",
                "UMG", 
                "GameAnimationSample",
                "OnlineSubsystem",
                // ... add other public dependencies that you statically link with here ...
            }
        );

        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "Slate",
                "SlateCore",
                "AnimationCore",
                "AnimGraphRuntime",
                "PhysicsCore", 
                "TargetingSystem"
                // ... add private dependencies that you statically link with here ...
            }
        );
        
        DynamicallyLoadedModuleNames.AddRange(
            new string[]
            {
                // ... add any modules that your module loads dynamically here ...
            }
        );
    }
}