{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "Calamity", "Description": "Core Plugin for Project Calamity", "Category": "Core", "CreatedBy": "KuroParadigm. (KuroPebbles)", "CreatedByURL": "", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "License": "", "CanContainContent": true, "IsBetaVersion": false, "IsExperimentalVersion": false, "Installed": false, "Modules": [{"Name": "Calamity", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "CalamityCore", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "CalamityEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}], "Plugins": [{"Name": "GameplayAbilities", "Enabled": true}, {"Name": "EnhancedInput", "Enabled": true}, {"Name": "ModularGameplay", "Enabled": true}, {"Name": "GameFeatures", "Enabled": true}, {"Name": "MotionWarping", "Enabled": true}, {"Name": "TargetingSystem", "Enabled": true}, {"Name": "PoseSearch", "Enabled": true}, {"Name": "EditorScriptingUtilities", "Enabled": true}, {"Name": "TargetingSystem", "Enabled": true}, {"Name": "Gauntlet", "Enabled": true}, {"Name": "GameplayStateTree", "Enabled": true}, {"Name": "StateTree", "Enabled": true}, {"Name": "CommonUI", "Enabled": true}, {"Name": "ModularGameplayActors", "Enabled": true}, {"Name": "GameplayAbilities", "Enabled": true}, {"Name": "EnhancedInput", "Enabled": true}, {"Name": "GameplayInteractions", "Enabled": true}, {"Name": "PhysicsControl", "Enabled": true}, {"Name": "<PERSON><PERSON><PERSON>", "Enabled": true}, {"Name": "NinjaCombat", "Enabled": true}, {"Name": "NinjaGAS", "Enabled": true}, {"Name": "NinjaInput", "Enabled": true}, {"Name": "NinjaInventory", "Enabled": true}, {"Name": "NinjaFactions", "Enabled": true}, {"Name": "AnimationLocomotionLibrary", "Enabled": true}, {"Name": "AnimationWarping", "Enabled": true}, {"Name": "Mutable", "Enabled": true}, {"Name": "MotionTrajectory", "Enabled": true}, {"Name": "Mover", "Enabled": true}, {"Name": "OnlineSubsystem", "Enabled": true}, {"Name": "ModelViewViewModel", "Enabled": true}]}