// // MuuKnighted Games. All rights reserved.
//
// #pragma once
//
// #include "BlendStack/AnimNode_BlendStack.h"
// #include "PoseSearch/PoseSearchLibrary.h"
// #include "AnimNode_MotionMatchingExtended.generated.h"
//
// class UPoseSearchDatabase;
//
// UENUM(BlueprintType)
// enum class EDistanceMatchState : uint8
// {
// 	/** Distance matching will be performed via an accumulate distance from a marker. */
// 	Start,
//
// 	/** Distance matching will be performed using a straight-line distance to a predicted stop location (i.e. where the
// 	 *  character is predicted to reach a velocity of zero). */
// 	Stop,
//
// 	/** Distance matching will be performed in two stages. First, the arc-length distance from the character to a predicted
// 	 *  pivot location will be used. (An arc path is used because the Pivot state has acceleration, which, in general, will
// 	 *  cause the character to follow a curved path rather than a straight-line path.) Second, after passing the Pivot
// 	 *  marker, distance will be accumulated as the character moves away from the Pivot marker, just like in the Start state. */
// 	Pivot
// };
//
// /** This is a hack because Epic did not use the POSESEARCH_API macro on the FMotionMatchingState struct that would expose
//  *  the struct to other modules. */
// struct ANIMATIONMATCHINGSUITE_API FMotionMatchingStateExtended
// {
// 	// Reset the state to a default state using the current Database
// 	void Reset(const FTransform& ComponentTransform);
//
// 	// Attempts to set the internal state to match the provided asset time including updating the internal DbPoseIdx. 
// 	// If the provided asset time is out of bounds for the currently playing asset then this function will reset the 
// 	// state back to the default state.
// 	void AdjustAssetTime(float AssetTime);
//
// 	// Internally stores the 'jump' to a new pose/sequence index and asset time for evaluation
// 	void JumpToPose(const FAnimationUpdateContext& Context, const UE::PoseSearch::FSearchResult& Result, int32 MaxActiveBlends, float BlendTime);
//
// 	void UpdateWantedPlayRate(const UE::PoseSearch::FSearchContext& SearchContext, const FFloatInterval& PlayRate, float TrajectorySpeedMultiplier);
//
// 	FVector GetEstimatedFutureRootMotionVelocity() const;
//
// 	UE::PoseSearch::FSearchResult CurrentSearchResult;
//
// 	// Time since the last pose jump
// 	float ElapsedPoseSearchTime = 0.f;
//
// 	// wanted PlayRate to have the selected animation playing at the estimated requested speed from the query.
// 	float WantedPlayRate = 1.f;
//
// 	// true if a new animation has been selected
// 	bool bJumpedToPose = false;
//
// 	UE::PoseSearch::FPoseIndicesHistory PoseIndicesHistory;
// };
//
//
// USTRUCT(BlueprintInternalUseOnly)
// struct ANIMATIONMATCHINGSUITE_API FAnimNode_MotionMatchingExtended : public FAnimNode_BlendStack_Standalone
// {
// 	GENERATED_BODY()
//
// public:
// 	/** Searches InDatabase instead of the Database property on this node. Use InterruptMode to control the continuing pose search */
// 	void SetDatabaseToSearch(UPoseSearchDatabase* InDatabase, EPoseSearchInterruptMode InterruptMode);
//
// 	/** Searches InDatabases instead of the Database property on the node. Use InterruptMode to control the continuing pose search. */
// 	void SetDatabasesToSearch(TConstArrayView<UPoseSearchDatabase*> InDatabases, EPoseSearchInterruptMode InterruptMode);
//
// 	/** Resets the effects of SetDatabaseToSearch/SetDatabasesToSearch and use the Database property on this node. */
// 	void ResetDatabasesToSearch(EPoseSearchInterruptMode InterruptMode);
//
// 	/** Uses InterruptMode to control the continuing pose search */
// 	void SetInterruptMode(EPoseSearchInterruptMode InterruptMode);
//
// 	const FMotionMatchingStateExtended& GetMotionMatchingState() const { return MotionMatchingState; }
//
// 	FVector GetEstimatedFutureRootMotionVelocity() const;
//
// 	const FAnimNodeFunctionRef& GetOnUpdateMotionMatchingStateFunction() const;
//
// 	void UpdateMotionMatchingState(
// 		const FAnimationUpdateContext& Context,
// 		const TArray<TObjectPtr<const UPoseSearchDatabase>>& Databases,
// 		float BlendTime,
// 		int32 MaxActiveBlends,
// 		const FFloatInterval& PoseJumpThresholdTime,
// 		float PoseReselectHistory,
// 		float SearchThrottleTime,
// 		const FFloatInterval& PlayRate,
// 		FMotionMatchingStateExtended& InOutMotionMatchingState,
// 		EPoseSearchInterruptMode InterruptMode = EPoseSearchInterruptMode::DoNotInterrupt,
// 		bool bShouldSearch = true,
// 		bool bShouldUseCachedChannelData = true,
// 		bool bDebugDrawQuery = false,
// 		bool bDebugDrawCurResult = false);
//
// 	bool IsInvalidatingContinuingPose(EPoseSearchInterruptMode InterruptMode, const UPoseSearchDatabase* CurrentResultDatabase, const TArray<TObjectPtr<const UPoseSearchDatabase>>& Databases);
// 	bool IsForceInterrupt(EPoseSearchInterruptMode InterruptMode, const UPoseSearchDatabase* CurrentResultDatabase, const TArray<TObjectPtr<const UPoseSearchDatabase>>& Databases);
// 	bool ShouldUseCachedChannelData(const UPoseSearchDatabase* CurrentResultDatabase, const TArray<TObjectPtr<const UPoseSearchDatabase>>& Databases);
// 	void TraceMotionMatching(UE::PoseSearch::FSearchContext& SearchContext, FMotionMatchingStateExtended& CurrentState, float DeltaTime, bool bSearch, float RecordingTime);
// 	
// protected:
//
// #pragma region FAnimNode_Base
// 	
// 	/** FAnimNode_Base interface */
// 	/** @todo: implement CacheBones_AnyThread to rebind the schema bones */
// 	virtual void Initialize_AnyThread(const FAnimationInitializeContext& Context) override;
// 	virtual void Evaluate_AnyThread(FPoseContext& Output) override;
// 	virtual bool GetIgnoreForRelevancyTest() const override;
// 	virtual bool SetIgnoreForRelevancyTest(bool bInIgnoreForRelevancyTest) override;
// 	virtual FName GetGroupName() const override;
// 	virtual EAnimGroupRole::Type GetGroupRole() const override;
// 	virtual EAnimSyncMethod GetGroupMethod() const override;
// 	virtual bool GetOverridePositionWhenJoiningSyncGroupAsLeader() const override;
// 	virtual bool IsLooping() const override;
// 	virtual bool SetGroupName(FName InGroupName) override;
// 	virtual bool SetGroupRole(EAnimGroupRole::Type InRole) override;
// 	virtual bool SetGroupMethod(EAnimSyncMethod InMethod) override;
// 	virtual bool SetOverridePositionWhenJoiningSyncGroupAsLeader(bool InOverridePositionWhenJoiningSyncGroupAsLeader) override;
// 	/** End of FAnimNode_Base interface */
// 	
// #pragma endregion 
//
// 	
// #pragma region FAnimNode_AssetPlayerBase
// 	
// 	/** FAnimNode_AssetPlayerBase interface */
// 	virtual void UpdateAssetPlayer(const FAnimationUpdateContext& Context) override;
// 	/** End of FAnimNode_AssetPlayerBase interface */
// 	
// #pragma endregion	 
//  
// 	
// 	const FVector& GetBlendspaceParameters() const;
// 	float GetBlendspaceParametersDeltaThreshold() const;
// 	EBlendStack_BlendspaceUpdateMode GetBlendspaceUpdateMode() const;
//
// #if WITH_EDITORONLY_DATA
// 	
// 	/** requested blend space blend parameters (if AnimationAsset is a blend space) */
// 	UPROPERTY(EditAnywhere, Category = "Blendspace", meta = (PinHiddenByDefault, FoldProperty))
// 	FVector BlendParameters = FVector::Zero();
//
// 	/** Use this to define a threshold to trigger a new blend when blendspace xy input pins change. */
// 	UPROPERTY(EditAnywhere, Category = "Blendspace", meta = (FoldProperty))
// 	float BlendParametersDeltaThreshold = 1000.0f;
//
// 	/** The group name that we synchronize with (NAME_None if it is not part of any group). Note that
// 	 *  this is the name of the group used to sync the output of this node - it will not force
// 	 *  syncing of animations contained by it. */
// 	UPROPERTY(EditAnywhere, Category = "Sync", meta = (FoldProperty))
// 	FName GroupName = NAME_None;
//
// 	/** The role this node can assume within the group (ignored if GroupName is not set). Note
// 	 *  that this is the role of the output of this node, not of animations contained by it. */
// 	UPROPERTY(VisibleAnywhere, Category = "Sync", meta = (FoldProperty))
// 	TEnumAsByte<EAnimGroupRole::Type> GroupRole = EAnimGroupRole::ExclusiveAlwaysLeader;
// 	
// 	/** When enabled, acting as the leader, and using marker-based sync, this asset player will not sync to the previous
// 	 *  leader's sync position when joining a sync group and before becoming the leader but instead force everyone else
// 	 *  to match its position. */
// 	UPROPERTY(VisibleAnywhere, Category = "Sync", meta = (FoldProperty, EditCondition = "GroupRole == EAnimGroupRole::AlwaysLeader || GroupRole == EAnimGroupRole::ExclusiveAlwaysLeader || GroupRole == EAnimGroupRole::TransitionLeader", EditConditionHides))
// 	bool bOverridePositionWhenJoiningSyncGroupAsLeader = true;
// 	
// 	/** How we should update individual blend space parameters. See dropdown options tooltips. */
// 	UPROPERTY(EditAnywhere, Category = "Blendspace", meta = (FoldProperty))
// 	EBlendStack_BlendspaceUpdateMode BlendspaceUpdateMode = EBlendStack_BlendspaceUpdateMode::UpdateAll;
//
// 	/** How this node will synchronize with other animations. Note that this determines how the output
// 	 *  of this node is used for synchronization, not of animations contained by it. */
// 	UPROPERTY(EditAnywhere, Category = "Sync", meta = (FoldProperty))
// 	EAnimSyncMethod Method = EAnimSyncMethod::DoNotSync;
//
// 	/** If true, "Relevant anim" nodes that look for the highest weighted animation in a state will ignore this node */
// 	UPROPERTY(EditAnywhere, Category = "Relevancy", meta = (FoldProperty, PinHiddenByDefault))
// 	bool bIgnoreForRelevancyTest = false;
//
// #endif
//
// 	/** Indicates whether the animation chosen by this node is driven by distance. */
// 	UPROPERTY(EditAnywhere, Category = "Distance Matching", meta=(PinHiddenByDefault))
// 	bool bIsDistanceMatching = false;
//
// 	// /** The state of distance matching. This parameter dictates how distance matching is performed internally. */
// 	// UPROPERTY(EditAnywhere, Category = "Settings", meta=(PinHiddenByDefault))
// 	// EDistanceMatchState DistanceMatchingState = EDistanceMatchState::Start;
//
// 	/** The vector clamp of the allowed playrate, such that (X = min, Y = max). This has no effect if both values are set to zero. */
// 	UPROPERTY(EditAnywhere, Category = "Distance Matching", meta = (PinHiddenByDefault))
// 	FVector2D PlayRateClamp = FVector2D(0.6f, 5.0f);
// 	
// 	/** The matching distance corresponding to the current animation being driven. Make sure the sign is correct relative
// 	 *  to the corresponding distance curve below. */
// 	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distance Matching", meta=(PinHiddenByDefault))
// 	float MatchingDistance = 0.0f;
//
// 	/** The name of the distance curve from which to extract data. (This curve on the animation must be monotonic increasing,
// 	 *  as is the case for the distance-matching approach in general.) */
// 	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distance Matching", meta=(PinHiddenByDefault))
// 	FName DistanceCurveName = FName(TEXT("Distance"));
//
// 	/** Indicates whether to advance the time of the Blend Space asset naturally (i.e. without distance matching.) This
// 	 *  will override any smoothing applied. */
// 	UPROPERTY(EditAnywhere, Category = "Distance Matching", meta = (PinHiddenByDefault))
// 	bool bAdvanceTimeNaturally = false;
//
// 	/** Indicates whether the distance value should be negated. If true, the input Matching Distance, if greater than 
// 	 *	zero, will be made negative for curve extraction purposes. NOTE: Negative inputs will not be modified. */
// 	UPROPERTY(EditAnywhere, Category = "Distance Matching", meta = (PinHiddenByDefault))
// 	bool bNegateDistanceValue = false;
//
// 	/** Indicates whether only the highest weighted sample (Sequence) should be used when calculating the normalized time used to drive the Blend Space player.
// 	 *	This will save some performance but will produce slightly different results than using all relevant samples to drive playback. */
// 	UPROPERTY(EditAnywhere, Category = "Distance Matching", meta = (PinHiddenByDefault))
// 	bool bUseOnlyHighestWeightedSampleForDistanceMatching = true;
//
// 	/** The smoothing alpha applied to the normalized time in the following way: PrevTime + (1 - SmoothingAlpha) * (TargetTime - PrevTime).
// 	 *  This smoothing is applied ON TOP OF the PlayRateClamp, meaning this smoothing alpha is best used in the Stop or PrePivot
// 	 *  states, for example, when the PlayRateClamp is zeroed (removing the clamp altogether). For best results, feed in a value
// 	 *  that begins at 0.5 and drops to 0 as the character approaches the distance-match marker. */
// 	UPROPERTY(EditAnywhere, Category = "Distance Matching", meta = (ClampMin = 0.0f, ClampMax = 0.99f, PinHiddenByDefault))
// 	float SmoothingAlpha = 0.0f;
// 	
// 	/** The database to search. This can be overridden by Anim Node Functions such as "On Become Relevant" and "On Update"
// 	 *  via SetDatabaseToSearch/SetDatabasesToSearch. */
// 	UPROPERTY(EditAnywhere, Category = "Settings", meta = (PinShownByDefault))
// 	TObjectPtr<const UPoseSearchDatabase> Database = nullptr;
//
// 	/** Time in seconds to blend out to the new pose. Uses either inertial blending, requiring an Inertialization node
// 	 *  after this node, or the internal blend stack, if MaxActiveBlends is greater than zero. */
// 	UPROPERTY(EditAnywhere, Category = "Settings", meta = (PinHiddenByDefault, ClampMin="0"))
// 	float BlendTime = 0.4f;
//
// 	/** Set Blend Profiles (editable in the skeleton) to determine how the blending is distributed among your character's
// 	 *  bones. It could be used to differentiate between upper body and lower body to blend timing. */
// 	UPROPERTY(EditAnywhere, Category = "Settings", meta = (PinHiddenByDefault, UseAsBlendProfile = true))
// 	TObjectPtr<UBlendProfile> BlendProfile;
//
// 	/** How the blend is applied over time to the bones. Common selections are linear, ease in, ease out, and ease in and out. */
// 	UPROPERTY(EditAnywhere, Category = "Settings", meta = (PinHiddenByDefault))
// 	EAlphaBlendOption BlendOption = EAlphaBlendOption::Linear;
//
// 	/** Don't jump to poses of the same segment that are within the interval this many seconds away from the continuing pose. */
// 	UPROPERTY(EditAnywhere, Category = "Settings", meta = (PinHiddenByDefault))
// 	FFloatInterval PoseJumpThresholdTime = FFloatInterval(0.0f, 0.0f);
//
// 	/** Prevent re-selection of poses that have been selected previously within this much time (in seconds) in the past.
// 	 *  This is across all animation segments that have been selected within this time range. */
// 	UPROPERTY(EditAnywhere, Category = "Settings", meta = (PinHiddenByDefault, ClampMin = "0"))
// 	float PoseReselectHistory = 0.3f;
//
// 	/** Minimum amount of time to wait between searching for a new pose segment. It allows users to define how often the
// 	 *  system searches, default for locomotion is searching every update, but you may only want to search once for other
// 	 *  situations, like jump. */
// 	UPROPERTY(EditAnywhere, Category = "Settings", meta = (PinHiddenByDefault, ClampMin="0"))
// 	float SearchThrottleTime = 0.0f;
//
// 	/** Effective range of play rate that can be applied to the animations to account for discrepancies in estimated velocity
// 	 *  between the movement model and the animation. */
// 	UPROPERTY(EditAnywhere, Category = "Settings", meta = (PinHiddenByDefault, ClampMin = "0.2", ClampMax = "3.0", UIMin = "0.2", UIMax = "3.0"))
// 	FFloatInterval PlayRate = FFloatInterval(1.f, 1.f);
// 	
// 	/** Experimental: Multiplier applied to the play rate of the selected animation after Motion Matching State has been updated. */
// 	UPROPERTY(EditAnywhere, Category = "Settings|Experimental", meta = (PinHiddenByDefault))
// 	float PlayRateMultiplier = 1.0f;
// 	
// 	UPROPERTY(EditAnywhere, Category = "Settings")
// 	bool bUseInertialBlend = false;
//
// 	/** Reset the motion matching selection state if it has become relevant to the graph after not being updated on previous
// 	 *  frames. */
// 	UPROPERTY(EditAnywhere, Category = "Settings")
// 	bool bResetOnBecomingRelevant = true;
//
// 	/** If set to false, the motion matching node will perform a search only if the continuing pose is invalid. This is
// 	 *  useful if you want to stagger searches of different nodes for performance reasons */
// 	UPROPERTY(EditAnywhere, Category = "Settings", meta = (PinHiddenByDefault))
// 	bool bShouldSearch = true;
//
// 	/** If set to true, the search of multiple databases with different schemas will try to share pose features data calculated during query build
// 	 *  the idea is to be able to share as much as possible the continuing pose features vector across different schemas (and potentially improve performances)
// 	 *  defaulted to false to preserve behavior backward compatibility */
// 	UPROPERTY(EditAnywhere, Category = "Settings", meta = (PinHiddenByDefault))
// 	bool bShouldUseCachedChannelData = false;
//
// 	/** Encapsulated motion matching algorithm and internal state */
// 	FMotionMatchingStateExtended  MotionMatchingState;
// 	
// 	/** Update Counter for detecting being relevant */
// 	FGraphTraversalCounter UpdateCounter;
//
// 	/** List of databases this node is searching. */
// 	UPROPERTY()
// 	TArray<TObjectPtr<const UPoseSearchDatabase>> DatabasesToSearch;
//
// 	/** Applied EPoseSearchInterruptMode on the next update that controls the continuing pose search eveluation. This is set back to EPoseSearchInterruptMode::DoNotInterrupt after each update. */
// 	EPoseSearchInterruptMode NextUpdateInterruptMode = EPoseSearchInterruptMode::DoNotInterrupt;
//
// 	/** True if the Database property on this node has been overridden by SetDatabaseToSearch/SetDatabasesToSearch. */
// 	bool bOverrideDatabaseInput = false;
//
// private:
// 	
// #if WITH_EDITORONLY_DATA
// 	
// 	UPROPERTY(meta=(FoldProperty))
// 	FAnimNodeFunctionRef OnMotionMatchingStateUpdated;
//
// #endif // WITH_EDITORONLY_DATA
// 	
// 	UPROPERTY()
// 	TObjectPtr<UAnimationAsset> AnimationAsset;
// 	
// 	UPROPERTY()
// 	TObjectPtr<UAnimationAsset> PrevAnimationAsset;
//
// 	bool bAnimationChanged = false;
// 	float AnimationTime = 0.0f;
// 	float PrevAnimationTime = 0.0f;
// 	int32 TriangulationIndex = 0;
// 	TArray<FBlendSampleData> BlendSampleData;
// 	bool bWasDistanceMatching = false;
// 	float DeltaAnimTime = 0.0f;
// 	float PrevDeltaTime = 0.0f;
// 	//float MatchingDistance = 0.0f;
// 	//EDistanceMatchState PrevDistanceMatchingState = EDistanceMatchState::Start;
// 	bool bDistanceMatchingStateChanged = false;
// 	bool bPassedMarker_Pivot = false;
// 	
// 	friend class UAnimGraphNode_MotionMatchingExtended;
// 	friend class UMotionMatchingAnimNodeLibrary;
// 	friend class UPoseSearchFeatureChannel_Trajectory;
// };
