// MuuKnighted Games. All rights reserved.


#include "AnimGraph/AnimGraphNode_SteeringExtended.h"
#include "Animation/AnimRootMotionProvider.h"
#include "Kismet2/BlueprintEditorUtils.h"
#include "Kismet2/CompilerResultsLog.h"
#include "DetailCategoryBuilder.h"
#include "DetailLayoutBuilder.h"
#include "PropertyHandle.h"
#include "ScopedTransaction.h"

#define LOCTEXT_NAMESPACE "AnimationSuiteNodes"

UAnimGraphNode_SteeringExtended::UAnimGraphNode_SteeringExtended(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer)
{
}

FText UAnimGraphNode_SteeringExtended::GetControllerDescription() const
{
	return LOCTEXT("SteeringExtended", "Steering Extended");
}

FText UAnimGraphNode_SteeringExtended::GetTooltipText() const
{
	return LOCTEXT("SteeringExtendedTooltip", "Rotates the root toward the target orientation.");
}

FText UAnimGraphNode_SteeringExtended::GetNodeTitle(ENodeTitleType::Type TitleType) const
{
	return GetControllerDescription();
}

FLinearColor UAnimGraphNode_SteeringExtended::GetNodeTitleColor() const
{
	return FLinearColor(0.0f, 0.56f, 1.0f);
	//return FLinearColor(FColor(154.0f, 0.0f, 123.0f));
}

void UAnimGraphNode_SteeringExtended::CustomizePinData(UEdGraphPin* Pin, FName SourcePropertyName, int32 ArrayIndex) const
{
	Super::CustomizePinData(Pin, SourcePropertyName, ArrayIndex);
	
	if (Pin->PinName == GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, OffsetRootNodeReference))
	{
		Pin->bHidden = Node.bUseTagToRetrieveOffsetRootData;
	}

	if (Pin->PinName == GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, OffsetRootBoneNodeTag))
	{
		Pin->bHidden = !Node.bUseTagToRetrieveOffsetRootData;
	}

	if (Pin->PinName == GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, CurrentAnimAsset))
	{
		Pin->bHidden = Node.bUseTagToRetrieveAnimPlayerData;
	}

	if (Pin->PinName == GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, CurrentAnimAssetTime))
	{
		Pin->bHidden = Node.bUseTagToRetrieveAnimPlayerData;
	}

	if (Pin->PinName == GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, AnimAssetPlayerTag))
	{
		Pin->bHidden = !Node.bUseTagToRetrieveAnimPlayerData;
	}

	if (Pin->PinName == GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, bUseCurveByName))
	{
		Pin->bHidden = !Node.bDriveOrientationByFloat;
	}

	if (Pin->PinName == GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, YawOrientationDelta))
	{
		Pin->bHidden = !Node.bDriveOrientationByFloat /* ||  !Node.bUseCurveByName */;
	}
	
	if (Pin->PinName == GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, RotationCurveName))
	{
		Pin->bHidden = !Node.bUseCurveByName || !Node.bDriveOrientationByFloat;
	}
	
	if (Pin->PinName == GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, ProceduralTargetTime))
	{
		Pin->bHidden = Node.bDriveOrientationByFloat;
	}

	if (Pin->PinName == GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, AnimatedTargetTime))
	{
		Pin->bHidden = Node.bDriveOrientationByFloat;
	}	
	
	if (Pin->PinName == GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, SmoothTargetStiffness))
	{
		Pin->bHidden = !Node.bEnableTargetSmoothing;
	}

	if (Pin->PinName == GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, SmoothTargetDamping))
	{
		Pin->bHidden = !Node.bEnableTargetSmoothing;
	}
}

void UAnimGraphNode_SteeringExtended::CustomizeDetails(IDetailLayoutBuilder& DetailBuilder)
{
	DECLARE_SCOPE_HIERARCHICAL_COUNTER_FUNC()
	Super::CustomizeDetails(DetailBuilder);

	DetailBuilder.SortCategories([](const TMap<FName, IDetailCategoryBuilder*>& CategoryMap)
	{
		for (const TPair<FName, IDetailCategoryBuilder*>& Pair : CategoryMap)
		{
			int32 SortOrder = Pair.Value->GetSortOrder();
			const FName CategoryName = Pair.Key;

			if (CategoryName == "Offset Root")
			{
				SortOrder += 1;
			}
			else if (CategoryName == "Animation")
			{
				SortOrder += 2;
			}
			else if (CategoryName == "Float")
			{
				SortOrder += 3;
			}
			else if (CategoryName == "Evaluation")
			{
				SortOrder += 4;
			}
			else if (CategoryName == "Filtering")
			{
				SortOrder += 5;
			}
			else
			{
				const int32 ValueSortOrder = Pair.Value->GetSortOrder();
				if (ValueSortOrder >= SortOrder && ValueSortOrder < SortOrder + 10)
				{
					SortOrder += 10;
				}
				else
				{
					continue;
				}
			}

			Pair.Value->SetSortOrder(SortOrder);
		}
	});

	TSharedRef<IPropertyHandle> NodeHandle = DetailBuilder.GetProperty(FName(TEXT("Node")), GetClass());
	
	// if (Node.bUseTagToRetrieveOffsetRootData)
	// {
	// 	DetailBuilder.HideProperty(NodeHandle->GetChildHandle(GET_MEMBER_NAME_CHECKED(FAnimNode_SteeringExtended, OffsetRootNodeReference)));
	// }
	// else
	// {
	// 	DetailBuilder.HideProperty(NodeHandle->GetChildHandle(GET_MEMBER_NAME_CHECKED(FAnimNode_SteeringExtended, OffsetRootBoneNodeTag)));
	// }
	//
	// if (Node.bUseTagToRetrieveAnimPlayerData)
	// {
	// 	DetailBuilder.HideProperty(NodeHandle->GetChildHandle(GET_MEMBER_NAME_CHECKED(FAnimNode_SteeringExtended, CurrentAnimAsset)));
	// 	DetailBuilder.HideProperty(NodeHandle->GetChildHandle(GET_MEMBER_NAME_CHECKED(FAnimNode_SteeringExtended, CurrentAnimAssetTime)));
	// }
	// else
	// {
	// 	DetailBuilder.HideProperty(NodeHandle->GetChildHandle(GET_MEMBER_NAME_CHECKED(FAnimNode_SteeringExtended, AnimAssetPlayerTag)));		
	// }
 //
 //    // Handle Orientation Drive parameters
 //    if (Node.bDriveOrientationByFloat)
 //    {
 //        // Hide time-based properties when using float-driven orientation
 //        DetailBuilder.HideProperty(NodeHandle->GetChildHandle(GET_MEMBER_NAME_CHECKED(FAnimNode_SteeringExtended, ProceduralTargetTime)));
 //        DetailBuilder.HideProperty(NodeHandle->GetChildHandle(GET_MEMBER_NAME_CHECKED(FAnimNode_SteeringExtended, AnimatedTargetTime)));
 //
 //        // Always show bUseCurveByName when float-driven
 //        TSharedRef<IPropertyHandle> UseCurveByNameHandle = NodeHandle->GetChildHandle(GET_MEMBER_NAME_CHECKED(FAnimNode_SteeringExtended, bUseCurveByName)).ToSharedRef();
 //        DetailBuilder.EditDefaultProperty(UseCurveByNameHandle);
 //
 //        // Get handles for the properties we'll be toggling
 //        TSharedRef<IPropertyHandle> RotationCurveNameHandle = NodeHandle->GetChildHandle(GET_MEMBER_NAME_CHECKED(FAnimNode_SteeringExtended, RotationCurveName)).ToSharedRef();
 //        TSharedRef<IPropertyHandle> YawOrientationDeltaHandle = NodeHandle->GetChildHandle(GET_MEMBER_NAME_CHECKED(FAnimNode_SteeringExtended, YawOrientationDelta)).ToSharedRef();
 //
 //        // Show/hide properties based on bUseCurveByName
 //        if (Node.bUseCurveByName)
 //        {
 //            DetailBuilder.EditDefaultProperty(RotationCurveNameHandle);
 //            DetailBuilder.HideProperty(YawOrientationDeltaHandle);
 //        }
 //        else
 //        {
 //            DetailBuilder.EditDefaultProperty(YawOrientationDeltaHandle);
 //            DetailBuilder.HideProperty(RotationCurveNameHandle);
 //        }
 //    }
 //    else
 //    {
 //        // Hide all float-driven orientation properties when not using float drive
 //        DetailBuilder.HideProperty(NodeHandle->GetChildHandle(GET_MEMBER_NAME_CHECKED(FAnimNode_SteeringExtended, bUseCurveByName)));
 //        DetailBuilder.HideProperty(NodeHandle->GetChildHandle(GET_MEMBER_NAME_CHECKED(FAnimNode_SteeringExtended, YawOrientationDelta)));
 //        DetailBuilder.HideProperty(NodeHandle->GetChildHandle(GET_MEMBER_NAME_CHECKED(FAnimNode_SteeringExtended, RotationCurveName)));
 //    }
 //
 //    // Handle Target Smoothing parameters
 //    if (!Node.bEnableTargetSmoothing)
 //    {
 //        DetailBuilder.HideProperty(NodeHandle->GetChildHandle(GET_MEMBER_NAME_CHECKED(FAnimNode_SteeringExtended, SmoothTargetStiffness)));
 //        DetailBuilder.HideProperty(NodeHandle->GetChildHandle(GET_MEMBER_NAME_CHECKED(FAnimNode_SteeringExtended, SmoothTargetDamping)));
 //    }
	
}

FText UAnimGraphNode_SteeringExtended::GetMenuCategory() const
{
	return LOCTEXT("AnimationMatchingSuite", "Animation Matching Suite");
}

FString UAnimGraphNode_SteeringExtended::GetNodeCategory() const
{
	return FString("Animation Matching Suite");
}

void UAnimGraphNode_SteeringExtended::PostEditChangeProperty(struct FPropertyChangedEvent& PropertyChangedEvent)
{
	DECLARE_SCOPE_HIERARCHICAL_COUNTER_FUNC()
	Super::PostEditChangeProperty(PropertyChangedEvent);

	bool bRequiresNodeReconstruct = false;
	FProperty* ChangedProperty = PropertyChangedEvent.Property;
	
	if (ChangedProperty)
	{
		if (ChangedProperty->GetFName() == GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, bUseTagToRetrieveOffsetRootData))
		{
			FScopedTransaction Transaction(LOCTEXT("ChangeUseTagToRetrieveOffsetRootData", "Change Use Tag To Retrieve Offset Root Data"));
			Modify();

			/** Break the link to the OffsetRootNodeReference pin. */
			UEdGraphPin* OffsetRootNodeReferencePin = FindPin(GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, OffsetRootNodeReference));
			if (OffsetRootNodeReferencePin && !OffsetRootNodeReferencePin->bNotConnectable)
			{
				OffsetRootNodeReferencePin->BreakAllPinLinks();
				bRequiresNodeReconstruct = true; 
			}

			/** Break the link to the OffsetRootBoneNodeTag pin. */
			UEdGraphPin* OffsetRootBoneNodeTagPin = FindPin(GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, OffsetRootBoneNodeTag));
			if (OffsetRootBoneNodeTagPin && !OffsetRootBoneNodeTagPin->bNotConnectable)
			{
				OffsetRootBoneNodeTagPin->BreakAllPinLinks();
				bRequiresNodeReconstruct = true; 
			}
		}

		if (ChangedProperty->GetFName() == GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, bUseTagToRetrieveAnimPlayerData))
		{
			FScopedTransaction Transaction(LOCTEXT("ChangeUseTagToRetrieveAnimPlayerData", "Change Use Tag To Retrieve Anim Player Data"));
			Modify();

			/** Break the link to the CurrentAnimAsset pin. */
			UEdGraphPin* CurrentAnimAssetPin = FindPin(GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, CurrentAnimAsset));
			if (CurrentAnimAssetPin && !CurrentAnimAssetPin->bNotConnectable)
			{
				CurrentAnimAssetPin->BreakAllPinLinks();
				bRequiresNodeReconstruct = true; 
			}

			/** Break the link to the CurrentAnimAssetTime pin. */
			UEdGraphPin* CurrentAnimAssetTimePin = FindPin(GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, CurrentAnimAssetTime));
			if (CurrentAnimAssetTimePin && !CurrentAnimAssetTimePin->bNotConnectable)
			{
				CurrentAnimAssetTimePin->BreakAllPinLinks();
				bRequiresNodeReconstruct = true; 
			}

			/** Break the link to the AnimAssetPlayerTag pin. */
			UEdGraphPin* AnimAssetPlayerTagPin = FindPin(GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, AnimAssetPlayerTag));
			if (AnimAssetPlayerTagPin && !AnimAssetPlayerTagPin->bNotConnectable)
			{
				AnimAssetPlayerTagPin->BreakAllPinLinks();
				bRequiresNodeReconstruct = true; 
			}
		}
		
		if (ChangedProperty->GetFName() == GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, bDriveOrientationByFloat))
		{ 
			FScopedTransaction Transaction(LOCTEXT("ChangeDriveOrientationByFloat", "Drive Orientation By Float"));
			Modify();

			/** Break the link to the UseCurveByName pin. */
			UEdGraphPin* UseCurveByNamePin = FindPin(GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, bUseCurveByName));
			if (UseCurveByNamePin && !UseCurveByNamePin->bNotConnectable)
			{
				UseCurveByNamePin->BreakAllPinLinks();
				bRequiresNodeReconstruct = true; 
			}

			/** Break the link to the YawOrientationDelta pin. */
			UEdGraphPin* YawOrientationDeltaPin = FindPin(GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, YawOrientationDelta));
			if (YawOrientationDeltaPin && !YawOrientationDeltaPin->bNotConnectable)
			{
				YawOrientationDeltaPin->BreakAllPinLinks();
				bRequiresNodeReconstruct = true; 
			}
			
			/** Break the link to the RotationCurveName pin. */
			UEdGraphPin* RotationCurveNamePin = FindPin(GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, RotationCurveName));
			if (RotationCurveNamePin && !RotationCurveNamePin->bNotConnectable)
			{
				RotationCurveNamePin->BreakAllPinLinks();
				bRequiresNodeReconstruct = true; 
			}
			
			/** Break the link to the ProceduralTargetTime pin. */
			UEdGraphPin* ProceduralTargetTimePin = FindPin(GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, ProceduralTargetTime));
			if (ProceduralTargetTimePin && !ProceduralTargetTimePin->bNotConnectable)
			{
				ProceduralTargetTimePin->BreakAllPinLinks();
				bRequiresNodeReconstruct = true; 
			}

			/** Break the link to the AnimatedTargetTime pin. */
			UEdGraphPin* AnimatedTargetTimePin = FindPin(GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, AnimatedTargetTime));
			if (AnimatedTargetTimePin && !AnimatedTargetTimePin->bNotConnectable)
			{
				AnimatedTargetTimePin->BreakAllPinLinks();
				bRequiresNodeReconstruct = true; 
			}
		}

		if (ChangedProperty->GetFName() == GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, bUseCurveByName))
		{
			FScopedTransaction Transaction(LOCTEXT("ChangeUseAnimCurveByName", "Change Use Anim Curve By Name"));
			Modify();

			/** Break the link to the RotationCurveName pin. */
			UEdGraphPin* RotationCurveNamePin = FindPin(GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, RotationCurveName));
			if (RotationCurveNamePin && !RotationCurveNamePin->bNotConnectable)
			{
				RotationCurveNamePin->BreakAllPinLinks();
				bRequiresNodeReconstruct = true; 
			}
			
			/** Break the link to the YawOrientationDelta pin. */
			UEdGraphPin* YawOrientationDeltaPin = FindPin(GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, YawOrientationDelta));
			if (YawOrientationDeltaPin && !YawOrientationDeltaPin->bNotConnectable)
			{
				YawOrientationDeltaPin->BreakAllPinLinks();
				bRequiresNodeReconstruct = true; 
			}
		}

		if (ChangedProperty->GetFName() == GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, bEnableTargetSmoothing))
		{
			FScopedTransaction Transaction(LOCTEXT("ChangeEnableTargetSmoothing", "Change Enable Target Smoothing"));
			Modify();

			/** Break the link to the SmoothTargetStiffness pin. */
			UEdGraphPin* SmoothTargetStiffnessPin = FindPin(GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, SmoothTargetStiffness));
			if (SmoothTargetStiffnessPin && !SmoothTargetStiffnessPin->bNotConnectable)
			{
				SmoothTargetStiffnessPin->BreakAllPinLinks();
				bRequiresNodeReconstruct = true; 
			}

			/** Break the link to the SmoothTargetDamping pin. */
			UEdGraphPin* SmoothTargetDampingPin = FindPin(GET_MEMBER_NAME_STRING_CHECKED(FAnimNode_SteeringExtended, SmoothTargetDamping));
			if (SmoothTargetDampingPin && !SmoothTargetDampingPin->bNotConnectable)
			{
				SmoothTargetDampingPin->BreakAllPinLinks();
				bRequiresNodeReconstruct = true; 
			}
		}

		if (bRequiresNodeReconstruct)
		{
			ReconstructNode();
			FBlueprintEditorUtils::MarkBlueprintAsStructurallyModified(GetBlueprint());
		}
	}
}

void UAnimGraphNode_SteeringExtended::GetInputLinkAttributes(FNodeAttributeArray& OutAttributes) const
{
	OutAttributes.Add(UE::Anim::IAnimRootMotionProvider::AttributeName);
}

void UAnimGraphNode_SteeringExtended::GetOutputLinkAttributes(FNodeAttributeArray& OutAttributes) const
{
	OutAttributes.Add(UE::Anim::IAnimRootMotionProvider::AttributeName);
}

void UAnimGraphNode_SteeringExtended::ValidateAnimNodeDuringCompilation(USkeleton* ForSkeleton, FCompilerResultsLog& MessageLog)
{
	Super::ValidateAnimNodeDuringCompilation(ForSkeleton, MessageLog);
}

#undef LOCTEXT_NAMESPACE
