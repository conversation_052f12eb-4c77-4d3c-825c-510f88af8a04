// Ninja Bear Studio Inc. 2024, all rights reserved.
#pragma once

#include "CoreMinimal.h"
#include "NativeGameplayTags.h"

NINJAGAS_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Tag_GAS_Ability_Passive);
NINJAGAS_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Tag_GAS_Activation_Fail_BlockedByTags);
NINJAGAS_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Tag_GAS_Activation_Fail_CantAffordCost);
NINJAGAS_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Tag_GAS_Activation_Fail_IsDead);
NINJAGAS_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Tag_GAS_Activation_Fail_MissingTags);
NINJAGAS_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Tag_GAS_Activation_Fail_Networking);
NINJAGAS_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Tag_GAS_Activation_Fail_OnCooldown);

// Shield related tags
NINJAGAS_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Tag_Ability_Shield);
NINJAGAS_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Tag_Ability_Shield_Active);
NINJAGAS_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Tag_Ability_Shield_Broken);
NINJAGAS_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Tag_Ability_Shield_Dome);
NINJAGAS_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Tag_Ability_Shield_Front);
NINJAGAS_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Tag_Ability_Shield_Cooldown);
NINJAGAS_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Tag_State_Alive);
NINJAGAS_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Tag_State_Dead);
NINJAGAS_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Tag_Event_Shield_TakeDamage);
NINJAGAS_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Tag_Effect_ShieldEffect_MovementReduction);
NINJAGAS_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Tag_Effect_ShieldEffect_DamageReduction);
NINJAGAS_API UE_DECLARE_GAMEPLAY_TAG_EXTERN(Tag_GameplayCue_Shield_Cooldown);
