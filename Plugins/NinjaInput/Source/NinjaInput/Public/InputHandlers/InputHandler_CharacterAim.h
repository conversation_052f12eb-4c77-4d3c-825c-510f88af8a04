// Ninja Bear Studio Inc., all rights reserved.
#pragma once

#include "CoreMinimal.h"
#include "NinjaInputHandler.h"
#include "InputHandler_CharacterAim.generated.h"

/**
 * Handles character aiming functionality.
 * Can be configured as either a toggle or hold-to-aim system.
 */
UCLASS(meta = (DisplayName = "Character: Aim"))
class NINJAINPUT_API UInputHandler_CharacterAim : public UNinjaInputHandler
{
    GENERATED_BODY()

public:
    UInputHandler_CharacterAim();

protected:
    /** Defines if aiming is toggled or momentary (hold). */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Aim", meta = (DisplayName = "Toggle Mode"))
    bool bToggle;

    // Called when the bToggle property is changed in the editor
    virtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override;

    /** Minimum magnitude required for aim to be accepted (for analog inputs). */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Aim")
    float MinimumMagnitudeToAim;

    /** If any of these tags are present, aiming is blocked. */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Aim")
    FGameplayTagContainer BlockAimTags;

    /** Checks if the character is allowed to aim. */
    UFUNCTION(BlueprintNativeEvent, Category = "Input Handler|Aim")
    bool CanAim(UNinjaInputManagerComponent* Manager, const FInputActionValue& Value) const;

    /** Evaluates input conditions to determine if the character should stop aiming. */
    UFUNCTION(BlueprintNativeEvent, Category = "Input Handler|Aim")
    bool ShouldStopAiming(UNinjaInputManagerComponent* Manager, const FInputActionValue& Value) const;

    virtual void HandleTriggeredEvent_Implementation(UNinjaInputManagerComponent* Manager,
        const FInputActionValue& Value, const UInputAction* InputAction, float ElapsedTime) const override;

    virtual void HandleCompletedEvent_Implementation(UNinjaInputManagerComponent* Manager,
        const FInputActionValue& Value, const UInputAction* InputAction) const override;
};