/*
*  Copyright (c) 2021 Damody(<EMAIL>).
*  All rights reserved.
*  @ Date : 2021/06/20
*
*/

#include "AnimNotifyState_GhostMesh.h"
#include "GameFramework/Character.h"
#include "Kismet/GameplayStatics.h"
#include <Kismet/KismetMathLibrary.h>
#include "GhostMeshActor.h"
#include "Materials/MaterialInterface.h"

UAnimNotifyState_GhostMesh::UAnimNotifyState_GhostMesh()
{
#if WITH_EDITORONLY_DATA
	NotifyColor = FColor(139, 0, 139, 255);
#endif // WITH_EDITORONLY_DATA
}

void UAnimNotifyState_GhostMesh::NotifyBegin(class USkeletalMeshComponent* MeshComp, class UAnimSequenceBase* Animation, float TotalDuration)
{
	TotalTime = TotalDuration;
	TimeCount = 0;
}

void UAnimNotifyState_GhostMesh::NotifyEnd(class USkeletalMeshComponent* MeshComp, class UAnimSequenceBase* Animation)
{
	TimeCount = 0;
}

void UAnimNotifyState_GhostMesh::NotifyTick(class USkeletalMeshComponent* MeshComp, class UAnimSequenceBase* Animation, float FrameDeltaTime)
{
        if (!IsValid(MeshComp))
	{
		return;
	}
	for (int32 i = 0; i < Ghosts.Num(); ++i)
	{
		if (!IsValid(Ghosts[i]))
		{
			Ghosts.RemoveAt(i);
		}
		else if (IsValid(Ghosts[i]))
		{
			Ghosts.RemoveAt(i);
		}
	}
	if (TimeCount >= TotalTime)
	{
		TimeCount -= TotalTime;
	}
	if (TimeCount2 >= Interval)
	{
		TimeCount2 -= Interval;
		BuildGhost(MeshComp, MeshComp->GetWorld());
		for (int32 i = 0; i < Ghosts.Num(); ++i)
		{
			int32 idx = Ghosts.Num() - i - 1;
			if (ReplaceMaterials.Num() > idx && IsValid(Ghosts[i]))
			{
				Ghosts[i]->ReplaceMaterial(ReplaceMaterials[idx], MaterialIndex);
			}
		}
	}
	
	TimeCount += FrameDeltaTime;
	TimeCount2 += FrameDeltaTime;
}

FString UAnimNotifyState_GhostMesh::GetNotifyName_Implementation() const
{
	return FString::Printf(TEXT("Ghost %.1f"), LifeTime);
}

void UAnimNotifyState_GhostMesh::BuildGhost(USkeletalMeshComponent* MeshComp, UWorld* const World)
{
	if (MeshComp->GetOwner())
	{
		AActor* owner = MeshComp->GetOwner();
		FTransform compTransform = MeshComp->GetComponentTransform();
		AGhostMeshActor* GhostPtr = World->SpawnActorDeferred<AGhostMeshActor>(
			AGhostMeshActor::StaticClass(), compTransform, nullptr, nullptr, ESpawnActorCollisionHandlingMethod::AlwaysSpawn);
		if (GhostPtr)
		{
			GhostPtr->LifeTime = LifeTime;
			GhostPtr->MakePoseBySkeletalMesh(MeshComp);
			if (ReplaceMaterials.Num() > 0)
			{
				GhostPtr->ReplaceMaterial(ReplaceMaterials[0], MaterialIndex);
			}
			UGameplayStatics::FinishSpawningActor(GhostPtr, compTransform);
			Ghosts.Add(GhostPtr);
		}
	}
}