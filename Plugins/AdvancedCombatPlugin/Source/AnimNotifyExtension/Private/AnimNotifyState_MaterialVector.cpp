/*
*  Copyright (c) 2021 Damody(<EMAIL>).
*  All rights reserved.
*  @ Date : 2021/06/20
*
*/
#include "AnimNotifyState_MaterialVector.h"
#include "Curves/CurveLinearColor.h"
#include "Materials/MaterialInstanceDynamic.h"

UAnimNotifyState_MaterialVector::UAnimNotifyState_MaterialVector()
{
#if WITH_EDITORONLY_DATA
	NotifyColor = FColor(224, 255, 255, 255);
#endif // WITH_EDITORONLY_DATA
}

void UAnimNotifyState_MaterialVector::NotifyBegin(class USkeletalMeshComponent* MeshComp, class UAnimSequenceBase* Animation, float TotalDuration)
{
	TotalTime = TotalDuration;
	TimeCount = 0;
}

void UAnimNotifyState_MaterialVector::NotifyEnd(class USkeletalMeshComponent* MeshComp, class UAnimSequenceBase* Animation)
{
	TimeCount = 0;
}

void UAnimNotifyState_MaterialVector::NotifyTick(class USkeletalMeshComponent* MeshComp, class UAnimSequenceBase* Animation, float FrameDeltaTime)
{
	if (ColorCurve)
	{
		if (TimeCount >= TotalTime)
		{
			TimeCount -= TotalTime;
		}
		TimeCount += FrameDeltaTime;
		FLinearColor Value = ColorCurve->GetLinearColorValue(TimeCount);
		UMaterialInterface* Material = MeshComp->GetMaterial(MaterialIndex);
		UMaterialInstanceDynamic* matInstance = MeshComp->CreateDynamicMaterialInstance(MaterialIndex, Material);
		if (matInstance != nullptr)
		{
			matInstance->SetVectorParameterValue(FName(Parameter), Value);
		}
	}
}

FString UAnimNotifyState_MaterialVector::GetNotifyName_Implementation() const
{
	if (ColorCurve)
	{
		FString Name1 = ColorCurve->GetName();
		Name1 = Name1.Replace(TEXT("_C"), TEXT(""));
		return FString::Printf(TEXT("%s"), *Name1);
	}
	return FString(TEXT("MaterialVector"));
}
