/*
*  Copyright (c) 2021 Damody(<EMAIL>).
*  All rights reserved.
*  @ Date : 2021/06/20
*
*/
#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "Curves/CurveFloat.h"
#include "AnimNotifyState_PlayRateCurve.generated.h"

/**
 * 
 */
UCLASS()
class ANIMNOTIFYEXTENSION_API UAnimNotifyState_PlayRateCurve : public UAnimNotifyState
{
	GENERATED_BODY()
public:
	UAnimNotifyState_PlayRateCurve();

	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	class UCurveFloat* RateCurve = nullptr;

	float TimeCount = 0;
	float TotalTime = 0;

	virtual void NotifyBegin(class USkeletalMeshComponent* MeshComp, class UAnimSequenceBase* Animation, float TotalDuration) override;
	virtual void NotifyEnd(class USkeletalMeshComponent* MeshComp, class UAnimSequenceBase* Animation) override;
	virtual void NotifyTick(class USkeletalMeshComponent* MeshComp, class UAnimSequenceBase* Animation, float FrameDeltaTime) override;

	FString GetNotifyName_Implementation() const;
};
