/*
*  Copyright (c) 2021 Damody(<EMAIL>).
*  All rights reserved.
*  @ Date : 2021/06/20
*
*/
#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "SplinePathActor.h"
#include "AnimNotifyState_SplineCurve.generated.h"

/**
 * 
 */
UCLASS()
class ANIMNOTIFYEXTENSION_API UAnimNotifyState_SplineCurve : public UAnimNotifyState
{
	GENERATED_BODY()
public:
	UAnimNotifyState_SplineCurve();

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Locomotion")
	TSubclassOf<ASplinePathActor> SplinePath;
	
	FVector StartPos;

	FRotator StartRot;

	float DebugCurrent = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Locomotion")
	bool DisableMove = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Locomotion")
	bool DisableRotator = false;

	bool InitEditor = false;
	
	virtual void NotifyBegin(class USkeletalMeshComponent* MeshComp, class UAnimSequenceBase* Animation, float TotalDuration) override;
	virtual void NotifyEnd(class USkeletalMeshComponent* MeshComp, class UAnimSequenceBase* Animation) override;
	virtual void NotifyTick(class USkeletalMeshComponent* MeshComp, class UAnimSequenceBase* Animation, float FrameDeltaTime) override;

	FString GetNotifyName_Implementation() const;
};
