// Ninja Bear Studio Inc., all rights reserved.
#pragma once

#include "CoreMinimal.h"
#include "GameplayEffect.h"
#include "CombatEffect_ParryVictimWindow.generated.h"

/**
 * Applies the Gameplay Tag that indicates a parry victim window.
 */
UCLASS()
class NINJACOMBAT_API UCombatEffect_ParryVictimWindow : public UGameplayEffect
{
	
	GENERATED_BODY()

public:

	UCombatEffect_ParryVictimWindow();
	
};
