// Ninja Bear Studio Inc., all rights reserved.
#pragma once

#include "CoreMinimal.h"
#include "GameplayEffectCustomApplicationRequirement.h"
#include "CombatRequirement_Parry.generated.h"

/**
 * A basic condition for a parry attack so it will only apply when both parry windows are open.
 */
UCLASS()
class NINJACOMBAT_API UCombatRequirement_Parry : public UGameplayEffectCustomApplicationRequirement
{
	
	GENERATED_BODY()

public:

	UCombatRequirement_Parry(const FObjectInitializer& ObjectInitializer);

	// -- Begin Custom Application Requirement implementation
	virtual bool CanApplyGameplayEffect_Implementation(const UGameplayEffect* GameplayEffect, const FGameplayEffectSpec& Spec, UAbilitySystemComponent* TargetASC) const override;
	// -- End Custom Application Requirement implementation

protected:

	/** <PERSON>le required to accept the parry. */
	UPROPERTY(EditDefaultsOnly, BlueprintReadWrite, Category = "Parry")
	float ParryAngle;
	
	/**
	 * Checks if the owner has died.
	 */
	virtual bool IsDead(const UAbilitySystemComponent* AbilityComponent) const;	
	
	/**
	 * Checks if the target is parrying our current attack.
	 */
	virtual bool IsParrying(const UAbilitySystemComponent* InstigatorASC, const UAbilitySystemComponent* TargetASC) const;

	/**
	 * Checks if the instigator (parry attacker) is facing the target.
	 * Considers the Parry Angle set in the effect class.
	 */
	virtual bool AreActorsFacingEachOther(const UAbilitySystemComponent* InstigatorASC, const UAbilitySystemComponent* TargetASC) const;
};
