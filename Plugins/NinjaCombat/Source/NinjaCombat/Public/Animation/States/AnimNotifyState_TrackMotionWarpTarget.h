// Ninja Bear Studio Inc., all rights reserved.
#pragma once

#include "CoreMinimal.h"
#include "Animation/NinjaCombatAnimNotifyState.h"
#include "AnimNotifyState_TrackMotionWarpTarget.generated.h"

class UTargetingPreset;

/**
 * Tracks a Motion Warp target, based on a given Targeting Preset.
 */
UCLASS(meta = (DisplayName = "Track Motion Warp Target"))
class NINJACOMBAT_API UAnimNotifyState_TrackMotionWarpTarget : public UNinjaCombatAnimNotifyState
{
	
	GENERATED_BODY()

public:

	/** The warp name that will be set in the Motion Warping Component. */
	UPROPERTY(EditAnywhere, Category = "Motion Warping")
	FName WarpName = "CombatWarp";
	
	/** Targeting Preset used for Motion Warping. */
	UPROPERTY(EditAnywhere, Category = "Motion Warping")
	TObjectPtr<UTargetingPreset> MotionWarpingTargetPreset;

	// -- Begin AnimNotifyState implementation
	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration, const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
	virtual FString GetNotifyName_Implementation() const override;
	// -- End AnimNotifyState implementation

};
