// Ninja Bear Studio Inc., all rights reserved.
#pragma once

#include "CoreMinimal.h"
#include "GameplayTagContainer.h"
#include "Animation/NinjaCombatAnimNotifyState.h"
#include "AnimNotifyState_ParryWindow.generated.h"

/**
 * Makes the owner open to a parry, during the animation frames covered by the state.
 */
UCLASS(meta = (DisplayName = "Parry Window"))
class NINJACOMBAT_API UAnimNotifyState_ParryWindow : public UNinjaCombatAnimNotifyState
{
	
	GENERATED_BODY()

public:
	
	UAnimNotifyState_ParryWindow();

	// -- Begin Anim Notify State implementation
	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration, const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
	virtual FString GetNotifyName_Implementation() const override;
	// -- End Anim Notify State implementation

protected:

	/** Gameplay Cue applied to the owner, when the parry window starts. */
	UPROPERTY(EditAnywhere, Category = "Parry Cue", meta = (Categories = "GameplayCue"))
	FGameplayTag ParryWindowGameplayCueTag;
	
private:

	bool bAddedTag = false;
	
};
