/*
*Product Name: MAUT
*Developer/Publisher: Demonrraz
*Date: 2024/11/14
*Unreal Engine Target Version: 5.5
*Marketplace URL: https://www.unrealengine.com/marketplace/en-US/product/97bf6ab1e948431ea72ed687bed82e18
*Support: https://discord.gg/sB2P2QGZp2
*/

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Character.h"
#include "AbilitySystemInterface.h"
#include "MAUT_AttributeSet.h"
#include "MAUT_Character.generated.h"

UCLASS()
class MAUT_API AMAUT_Character : public ACharacter, public IAbilitySystemInterface //Required this interface to use GAS on this character's class

{
	GENERATED_BODY()

public:
	// Sets default values for this character's properties
	AMAUT_Character();

#pragma region MAUT //Setup interface's functions and components
	// Helper function to get our ability system component (Used by the IAbilitySystemInterface).
	UFUNCTION(BlueprintPure, Category = "MAUT Character | MAUT Ability System Component")
	UAbilitySystemComponent* GetAbilitySystemComponent() const override;

	//Gameplay Ability System Component.
	UPROPERTY(BlueprintReadOnly, Category = "MAUT Character | MAUT Ability System Component")
	class UAbilitySystemComponent* MAUT_AbilitySystemComponent;

	//AttributeSet.
	UPROPERTY(BlueprintReadOnly, Category = "MAUT Character | MAUT Attribute Set")
	class UMAUT_AttributeSet* MAUT_AttributeSet;
#pragma endregion MAUT

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

#pragma region MAUT //Setup the override functions and implemment the interface's function
	// Called when our controller changes.
	void PossessedBy(AController* NewController) override;

	// Called when our PlayerState is assigned.
	void OnRep_PlayerState() override;

	// Sets up the ability system component.
	void SetAbilitySystemComponent();
#pragma endregion MAUT

public:	
	// Called every frame
	virtual void Tick(float DeltaTime) override;

	// Called to bind functionality to input
	virtual void SetupPlayerInputComponent(class UInputComponent* PlayerInputComponent) override;

};
