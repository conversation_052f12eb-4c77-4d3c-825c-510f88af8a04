//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a RdGen v1.13.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#include "LiveCodingModel.Pregenerated.h"


#include "RdEditorRoot/RdEditorRoot.Pregenerated.h"

#ifdef _MSC_VER
#pragma warning( push )
#pragma warning( disable:4250 )
#pragma warning( disable:4307 )
#pragma warning( disable:4267 )
#pragma warning( disable:4244 )
#pragma warning( disable:4100 )
#endif

namespace JetBrains {
namespace EditorPlugin {
// companion

LiveCodingModel::LiveCodingModelSerializersOwner const LiveCodingModel::serializersOwner;

void LiveCodingModel::LiveCodingModelSerializersOwner::registerSerializersCore(rd::Serializers const& serializers) const
{
}


// extension
LiveCodingModel const & LiveCodingModel::getOrCreateExtensionOf(RdEditorModel & pointcut)
{
    return pointcut.getOrCreateExtension<LiveCodingModel>("liveCodingModel");
    
}
// constants
// initializer
void LiveCodingModel::initialize()
{
    serializationHash = -1876100804104359916L;
}
// primary ctor
LiveCodingModel::LiveCodingModel(rd::RdEndpoint<rd::Void, bool, rd::Polymorphic<rd::Void>, rd::Polymorphic<bool>> lC_IsEnabledByDefault_, rd::RdSignal<bool, rd::Polymorphic<bool>> lC_EnableByDefault_, rd::RdEndpoint<rd::Void, bool, rd::Polymorphic<rd::Void>, rd::Polymorphic<bool>> lC_IsEnabledForSession_, rd::RdEndpoint<rd::Void, bool, rd::Polymorphic<rd::Void>, rd::Polymorphic<bool>> lC_CanEnableForSession_, rd::RdSignal<bool, rd::Polymorphic<bool>> lC_EnableForSession_, rd::RdEndpoint<rd::Void, bool, rd::Polymorphic<rd::Void>, rd::Polymorphic<bool>> lC_IsCompiling_, rd::RdEndpoint<rd::Void, bool, rd::Polymorphic<rd::Void>, rd::Polymorphic<bool>> lC_HasStarted_, rd::RdSignal<rd::Void, rd::Polymorphic<rd::Void>> lC_Compile_, rd::RdSignal<rd::Void, rd::Polymorphic<rd::Void>> lC_OnPatchComplete_) :
rd::RdExtBase()
,lC_IsEnabledByDefault_(std::move(lC_IsEnabledByDefault_)), lC_EnableByDefault_(std::move(lC_EnableByDefault_)), lC_IsEnabledForSession_(std::move(lC_IsEnabledForSession_)), lC_CanEnableForSession_(std::move(lC_CanEnableForSession_)), lC_EnableForSession_(std::move(lC_EnableForSession_)), lC_IsCompiling_(std::move(lC_IsCompiling_)), lC_HasStarted_(std::move(lC_HasStarted_)), lC_Compile_(std::move(lC_Compile_)), lC_OnPatchComplete_(std::move(lC_OnPatchComplete_))
{
    initialize();
}
// secondary constructor
// default ctors and dtors
LiveCodingModel::LiveCodingModel()
{
    initialize();
}
// reader
// writer
// virtual init
void LiveCodingModel::init(rd::Lifetime lifetime) const
{
    rd::RdExtBase::init(lifetime);
    bindPolymorphic(lC_IsEnabledByDefault_, lifetime, this, "lC_IsEnabledByDefault");
    bindPolymorphic(lC_EnableByDefault_, lifetime, this, "lC_EnableByDefault");
    bindPolymorphic(lC_IsEnabledForSession_, lifetime, this, "lC_IsEnabledForSession");
    bindPolymorphic(lC_CanEnableForSession_, lifetime, this, "lC_CanEnableForSession");
    bindPolymorphic(lC_EnableForSession_, lifetime, this, "lC_EnableForSession");
    bindPolymorphic(lC_IsCompiling_, lifetime, this, "lC_IsCompiling");
    bindPolymorphic(lC_HasStarted_, lifetime, this, "lC_HasStarted");
    bindPolymorphic(lC_Compile_, lifetime, this, "lC_Compile");
    bindPolymorphic(lC_OnPatchComplete_, lifetime, this, "lC_OnPatchComplete");
}
// identify
void LiveCodingModel::identify(const rd::Identities &identities, rd::RdId const &id) const
{
    rd::RdBindableBase::identify(identities, id);
    identifyPolymorphic(lC_IsEnabledByDefault_, identities, id.mix(".lC_IsEnabledByDefault"));
    identifyPolymorphic(lC_EnableByDefault_, identities, id.mix(".lC_EnableByDefault"));
    identifyPolymorphic(lC_IsEnabledForSession_, identities, id.mix(".lC_IsEnabledForSession"));
    identifyPolymorphic(lC_CanEnableForSession_, identities, id.mix(".lC_CanEnableForSession"));
    identifyPolymorphic(lC_EnableForSession_, identities, id.mix(".lC_EnableForSession"));
    identifyPolymorphic(lC_IsCompiling_, identities, id.mix(".lC_IsCompiling"));
    identifyPolymorphic(lC_HasStarted_, identities, id.mix(".lC_HasStarted"));
    identifyPolymorphic(lC_Compile_, identities, id.mix(".lC_Compile"));
    identifyPolymorphic(lC_OnPatchComplete_, identities, id.mix(".lC_OnPatchComplete"));
}
// getters
rd::RdEndpoint<rd::Void, bool, rd::Polymorphic<rd::Void>, rd::Polymorphic<bool>> const & LiveCodingModel::get_lC_IsEnabledByDefault() const
{
    return lC_IsEnabledByDefault_;
}
rd::ISource<bool> const & LiveCodingModel::get_lC_EnableByDefault() const
{
    return lC_EnableByDefault_;
}
rd::RdEndpoint<rd::Void, bool, rd::Polymorphic<rd::Void>, rd::Polymorphic<bool>> const & LiveCodingModel::get_lC_IsEnabledForSession() const
{
    return lC_IsEnabledForSession_;
}
rd::RdEndpoint<rd::Void, bool, rd::Polymorphic<rd::Void>, rd::Polymorphic<bool>> const & LiveCodingModel::get_lC_CanEnableForSession() const
{
    return lC_CanEnableForSession_;
}
rd::ISource<bool> const & LiveCodingModel::get_lC_EnableForSession() const
{
    return lC_EnableForSession_;
}
rd::RdEndpoint<rd::Void, bool, rd::Polymorphic<rd::Void>, rd::Polymorphic<bool>> const & LiveCodingModel::get_lC_IsCompiling() const
{
    return lC_IsCompiling_;
}
rd::RdEndpoint<rd::Void, bool, rd::Polymorphic<rd::Void>, rd::Polymorphic<bool>> const & LiveCodingModel::get_lC_HasStarted() const
{
    return lC_HasStarted_;
}
rd::ISource<rd::Void> const & LiveCodingModel::get_lC_Compile() const
{
    return lC_Compile_;
}
rd::ISignal<rd::Void> const & LiveCodingModel::get_lC_OnPatchComplete() const
{
    return lC_OnPatchComplete_;
}
// intern
// equals trait
// equality operators
bool operator==(const LiveCodingModel &lhs, const LiveCodingModel &rhs) {
    return &lhs == &rhs;
}
bool operator!=(const LiveCodingModel &lhs, const LiveCodingModel &rhs){
    return !(lhs == rhs);
}
// hash code trait
// type name trait
// static type name trait
// polymorphic to string
std::string LiveCodingModel::toString() const
{
    std::string res = "LiveCodingModel\n";
    res += "\tlC_IsEnabledByDefault = ";
    res += rd::to_string(lC_IsEnabledByDefault_);
    res += '\n';
    res += "\tlC_EnableByDefault = ";
    res += rd::to_string(lC_EnableByDefault_);
    res += '\n';
    res += "\tlC_IsEnabledForSession = ";
    res += rd::to_string(lC_IsEnabledForSession_);
    res += '\n';
    res += "\tlC_CanEnableForSession = ";
    res += rd::to_string(lC_CanEnableForSession_);
    res += '\n';
    res += "\tlC_EnableForSession = ";
    res += rd::to_string(lC_EnableForSession_);
    res += '\n';
    res += "\tlC_IsCompiling = ";
    res += rd::to_string(lC_IsCompiling_);
    res += '\n';
    res += "\tlC_HasStarted = ";
    res += rd::to_string(lC_HasStarted_);
    res += '\n';
    res += "\tlC_Compile = ";
    res += rd::to_string(lC_Compile_);
    res += '\n';
    res += "\tlC_OnPatchComplete = ";
    res += rd::to_string(lC_OnPatchComplete_);
    res += '\n';
    return res;
}
// external to string
std::string to_string(const LiveCodingModel & value)
{
    return value.toString();
}
}
}

#ifdef _MSC_VER
#pragma warning( pop )
#endif

