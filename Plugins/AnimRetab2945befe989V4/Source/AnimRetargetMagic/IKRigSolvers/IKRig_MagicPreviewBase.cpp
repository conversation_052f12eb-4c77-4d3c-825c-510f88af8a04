// Copyright 2024 Fengzz. All Rights Reserved.


#include "IKRig_MagicPreviewBase.h"



#if WITH_EDITOR
void UIKRig_MagicPreviewBase::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
	FProperty* PropertyThatChanged = PropertyChangedEvent.Property;

	// Set the skinned asset pointer with the alias pointer (must happen before the call to Super::PostEditChangeProperty)
	PRAGMA_DISABLE_DEPRECATION_WARNINGS
	if (PropertyThatChanged)
	{
		if (PropertyThatChanged->GetFName() == GET_MEMBER_NAME_CHECKED(UIKRig_MagicPreviewBase, AttachPreviewSettings))
		{
			OnAttachSettingsChanged();
		}
		else if (PropertyChangedEvent.MemberProperty &&
			PropertyChangedEvent.MemberProperty->GetFName() == GET_MEMBER_NAME_CHECKED(UIKRig_MagicPreviewBase, AttachPreviewSettings))
		{
			OnAttachSettingsChanged();
		}

	}
	PRAGMA_ENABLE_DEPRECATION_WARNINGS

	Super::PostEditChangeProperty(PropertyChangedEvent);
}

FMagicDebugPreviewSettings UIKRig_MagicPreviewBase::GetDebugPreviewSettings() const
{
	return FMagicDebugPreviewSettings(AttachPreviewSettings);
}

void UIKRig_MagicPreviewBase::OnAttachSettingsChanged()
{
	// empty.
}

void UIKRig_MagicPreviewBase::CustomPreviewDraw(UMagicAttachPreviewComponent* PreviewComp)
{
	// empty.
}

#endif
