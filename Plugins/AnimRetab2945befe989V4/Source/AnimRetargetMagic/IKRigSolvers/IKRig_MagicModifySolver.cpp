// Copyright 2023 Fengzz. All Rights Reserved.


#include "IKRig_MagicModifySolver.h"
#include "IKRigMagicUtilities.h"

#include UE_INLINE_GENERATED_CPP_BY_NAME(IKRig_MagicModifySolver)

#define LOCTEXT_NAMESPACE "UIKRig_MagicModifySolver"

UIKRig_MagicModifySolver::UIKRig_MagicModifySolver()
{
	BoneSettings = CreateDefaultSubobject<UIKRig_MagicModifyBoneSettings>(TEXT("BoneSettings"));
}

void UIKRig_MagicModifySolver::Initialize(const FIKRigSkeleton& IKRigSkeleton)
{
	InitBoneCache(IKRigSkeleton);
}
void UIKRig_MagicModifySolver::InitBoneCache(const FIKRigSkeleton& IKRigSkeleton)
{
	bIsBoneSettingDirty = false;
	ResetCache();

	if (!BoneSettings || !BoneSettings->IsModifyActive())
	{
		return;
	}
	BoneSettings->Bone = RootBone;
	CachedRootBoneIndex = IKRigSkeleton.GetBoneIndexFromName(RootBone);
	if (!IsValidBoneCache())
	{
		return;
	}

	if (BoneSettings->bPropagateToChilds)
	{
		FMagicSolverUtilities::GatherAllChilds(CachedRootBoneIndex, IKRigSkeleton, CachedChilds);
	}
}

void UIKRig_MagicModifySolver::Solve(FIKRigSkeleton& IKRigSkeleton, const FIKRigGoalContainer& Goals)
{
	if (bIsBoneSettingDirty)
	{
		InitBoneCache(IKRigSkeleton);
	}

	if (!IsValidBoneCache() || !BoneSettings)
	{
		return;
	}

	// Update local transform value first. It may be used later.
	IKRigSkeleton.UpdateLocalTransformFromGlobal(CachedRootBoneIndex);

	// Refresh local data first before modify current bone.
	if (CachedChilds.Num() > 0)
	{
		// propagate to childs
		for (int32 boneIndex : CachedChilds)
		{
			IKRigSkeleton.UpdateLocalTransformFromGlobal(boneIndex);
		}
	}

	TArray<FTransform>& InOutTransforms = IKRigSkeleton.CurrentPoseGlobal;

	FTransform NewGlobalTM = InOutTransforms[CachedRootBoneIndex];

	// modify scale
	if (BoneSettings->ScaleMode != EMagicBoneModifyMode::IgnoreMode)
	{
		FVector NewComp = BoneSettings->Scale;

		FMagicSolverUtilities::MagicCalSourceToGlobal<FTMCompTool_Scale>(IKRigSkeleton, NewComp, CachedRootBoneIndex, BoneSettings->ScaleSpace, BoneSettings->ScaleMode);
		if (!FMath::IsNearlyEqual(BoneSettings->ScaleAlphaX, 1.0f, UE_KINDA_SMALL_NUMBER) ||
			!FMath::IsNearlyEqual(BoneSettings->ScaleAlphaY, 1.0f, UE_KINDA_SMALL_NUMBER) ||
			!FMath::IsNearlyEqual(BoneSettings->ScaleAlphaZ, 1.0f, UE_KINDA_SMALL_NUMBER))
		{
			FVector OldVal = InOutTransforms[CachedRootBoneIndex].GetTranslation();
			FVector Alpha(BoneSettings->ScaleAlphaX, BoneSettings->ScaleAlphaY, BoneSettings->ScaleAlphaZ);
			NewComp = FMath::Lerp(OldVal, NewComp, Alpha);
		}
		NewGlobalTM.SetScale3D(NewComp);
	}
	// modify rotation
	if (BoneSettings->RotationMode != EMagicBoneModifyMode::IgnoreMode)
	{
		FQuat NewComp = BoneSettings->Rotation.Quaternion();
		FMagicSolverUtilities::MagicCalSourceToGlobal<FTMCompTool_Quat>(IKRigSkeleton, NewComp, CachedRootBoneIndex, BoneSettings->RotationSpace, BoneSettings->RotationMode);

		if (!FMath::IsNearlyEqual(BoneSettings->RotationAlpha, 1.0f, UE_KINDA_SMALL_NUMBER))
		{			
			FQuat OldVal = InOutTransforms[CachedRootBoneIndex].GetRotation();
			NewComp = FMagicMath::OptionalLerp(OldVal, NewComp, BoneSettings->RotationAlpha);
		}

		NewGlobalTM.SetRotation(NewComp);
	}

	// modify location
	if (BoneSettings->TranslationMode != EMagicBoneModifyMode::IgnoreMode || BoneSettings->TranslationRotMode != EMagicBoneModifyMode::IgnoreMode)
	{
		FVector NewComp = BoneSettings->Translation;
		FMagicSolverUtilities::MagicCalSourceToGlobal<FTMCompTool_Loc>(IKRigSkeleton, NewComp, CachedRootBoneIndex, BoneSettings->TranslationSpace, BoneSettings->TranslationMode);

		// modify translation rotation
		if (BoneSettings->TranslationRotMode != EMagicBoneModifyMode::IgnoreMode)
		{
			if (BoneSettings->TranslationMode == EMagicBoneModifyMode::IgnoreMode)
			{
				// if translation is ignored, we need to get the current translation value.
				NewComp = InOutTransforms[CachedRootBoneIndex].GetTranslation();
			}

			FQuat NewQuatComp = BoneSettings->TranslationRot.Quaternion();
			FMagicSolverUtilities::MagicCalSourceToGlobal<FTMCompTool_Quat>(IKRigSkeleton, NewQuatComp, CachedRootBoneIndex, BoneSettings->TranslationRotSpace, BoneSettings->TranslationRotMode);
			NewComp = NewQuatComp.RotateVector(NewComp);
		}

		if (!FMath::IsNearlyEqual(BoneSettings->TranslationAlphaX, 1.0f, UE_KINDA_SMALL_NUMBER) ||
			!FMath::IsNearlyEqual(BoneSettings->TranslationAlphaY, 1.0f, UE_KINDA_SMALL_NUMBER) ||
			!FMath::IsNearlyEqual(BoneSettings->TranslationAlphaZ, 1.0f, UE_KINDA_SMALL_NUMBER))
		{

			FVector OldVal = InOutTransforms[CachedRootBoneIndex].GetTranslation();
			FVector Alpha(BoneSettings->TranslationAlphaX, BoneSettings->TranslationAlphaY, BoneSettings->TranslationAlphaZ);
			NewComp = FMath::Lerp(OldVal, NewComp, Alpha);
		}

		NewGlobalTM.SetLocation(NewComp);
	}

	InOutTransforms[CachedRootBoneIndex] = NewGlobalTM;

	if (BoneSettings->bRefreshLocalTransfom)
	{
		IKRigSkeleton.UpdateLocalTransformFromGlobal(CachedRootBoneIndex);
	}

	if (CachedChilds.Num() > 0)
	{
		// propagate to childs
		for (int32 boneIndex : CachedChilds)
		{
			IKRigSkeleton.UpdateGlobalTransformFromLocal(boneIndex);
		}
	}
}

void UIKRig_MagicModifySolver::UpdateSolverSettings(UIKRigSolver* InSettings)
{
	Super::UpdateSolverSettings(InSettings);
	if (UIKRig_MagicModifySolver* Solver = Cast<UIKRig_MagicModifySolver>(InSettings))
	{
		if (BoneSettings && Solver->BoneSettings)
		{
			bIsBoneSettingDirty |= (!BoneSettings->IsBoneSettingsEqual(Solver->BoneSettings));
			BoneSettings->UpdateSettings(Solver->BoneSettings);
			
		}
	}
}

#if WITH_EDITOR

FText UIKRig_MagicModifySolver::GetNiceName() const
{
	return FormatNiceName(TEXT("Magic Modify"));
}

bool UIKRig_MagicModifySolver::GetWarningMessage(FText& OutWarningMessage) const
{
	if (RootBone == NAME_None)
	{
		OutWarningMessage = LOCTEXT("MissingRoot", "Missing root bone.");
		return true;
	}

	if (!BoneSettings || !BoneSettings->IsModifyActive())
	{
		OutWarningMessage = LOCTEXT("NoModify", "No modify mode is active.");
		return true;
	}

	return false;
}

void UIKRig_MagicModifySolver::AddBoneSetting(const FName& BoneName)
{
	if (BoneSettings)
	{
		BoneSettings->Bone = BoneName;
		RootBone = BoneName;
	}
}

void UIKRig_MagicModifySolver::RemoveBoneSetting(const FName& BoneName) 
{
	if (RootBone == BoneName)
	{
		BoneSettings->Bone = NAME_None;
		RootBone = NAME_None;
	}
}
UObject* UIKRig_MagicModifySolver::GetBoneSetting(const FName& BoneName) const 
{
	if (BoneSettings && RootBone == BoneName)
	{
		return BoneSettings;
	}
	return nullptr;
}

void UIKRig_MagicModifySolver::SetRootBone(const FName& RootBoneName)
{
	RootBone = RootBoneName;
	if (BoneSettings)
	{
		BoneSettings->Bone = RootBone;
	}
}

bool UIKRig_MagicModifySolver::IsBoneAffectedBySolver(const FName& BoneName, const FIKRigSkeleton& IKRigSkeleton) const
{
	return IKRigSkeleton.IsBoneInDirectLineage(BoneName, RootBone);
}

#endif


#undef LOCTEXT_NAMESPACE