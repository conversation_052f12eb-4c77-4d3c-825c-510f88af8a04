// Copyright 2023 Fengzz. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Toolkits/IToolkitHost.h"
#include "AssetTypeActions_Base.h"
#include "EditorAnimUtils.h"
#include "WorkflowOrientedApp/WorkflowCentricApplication.h"


class UIKRetargeter;
class IAssetEditorInstance;

class FApplicationMode;

class ANIMRETARGETMAGICEDITOR_API FAssetTypeActions_MagicIKRetargeter //: public FAssetTypeActions_Base
{
public:

	static void ExtendAnimSequenceToolMenu();
	static void CreateRetargetSubMenu(FToolMenuSection& InSection);

	static void OnAssetOpened(UObject* Object, IAssetEditorInstance* Instance);

	static TSharedRef<FApplicationMode> ExtendApplicationMode(const FName ModeName, TSharedRef<FApplicationMode> InMode);

	static void OnRegisterTabs(FWorkflowAllowedTabSet& TabSet, TSharedPtr<FAssetEditorToolkit> HostApp);
};
