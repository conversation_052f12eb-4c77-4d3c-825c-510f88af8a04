// Copyright 2023 Fengzz. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "WorkflowOrientedApp/WorkflowTabFactory.h"

class FAssetEditorToolkit;
class FWorkflowTabFactory;
class UIKRetargeter;
class <PERSON>IKRetargeterController;

class <PERSON>IM<PERSON><PERSON>R<PERSON>TMAGICEDITOR_API FMagicIKRetargetAssetBrowserTabSummoner : public FWorkflowTabFactory
{
public:
	static const FName TabID;
	
	FMagicIKRetargetAssetBrowserTabSummoner(TSharedPtr<FWorkflowTabFactory> InBaseFactroy,const TSharedRef<FAssetEditorToolkit>& InIKRetargetEditor, UIKRetargeter* InRetargeterAsset);
	virtual TSharedRef<SWidget> CreateTabBody(const FWorkflowTabSpawnInfo& Info) const override;
	virtual TSharedPtr<SToolTip> CreateTabToolTipWidget(const FWorkflowTabSpawnInfo& Info) const override;

protected:

	void ExtendTabBody(TSharedRef<SWidget>& TabBody, const FWorkflowTabSpawnInfo& Info) const;

	TSharedPtr<FWorkflowTabFactory> BaseFactroy;

	TWeakObjectPtr<UIKRetargeter> RetargeterAsset;
};
